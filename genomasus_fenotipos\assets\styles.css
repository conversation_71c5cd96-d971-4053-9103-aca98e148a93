/* assets/styles.css */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap');

:root {
  --primary-color: #87F2F4;
  --secondary-color: #292A2D;
  --background-color: #FAFAFA;
  --text-color: #212327;
  --light-text-color: #F1F1F1;
  --font-family: 'Poppins', sans-serif;
}

/* Global styles */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  margin: 0;
  padding: 0;
  background-color: var(--background-color);
  overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family);
  font-weight: 600;
  color: var(--secondary-color);
  margin-top: 0;
}

h1 {
  font-size: clamp(20px, 5vw, 24px);
}

p, a, span, li {
  font-family: var(--font-family);
  font-weight: 400;
  color: var(--light-text-color);
}

a {
  text-decoration: none;
  color: var(--light-text-color);
  transition: color 0.3s ease;
}

a:hover {
  color: var(--background-color);
}

/* Estilo para o dcc.Link para se comportar como html.A */
a._dash-link {
  text-decoration: none;
  color: inherit;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.dashboard-card:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

/* Navbar styles - Cores específicas por dashboard */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 20px;
  /* background-color e color definidos dinamicamente via inline styles */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  min-height: 60px;
}

.navbar h1 {
  margin: 0;
  font-size: 22px;
  color: #ffffff;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
}

.navbar-links {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: nowrap;
}

.menu-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 24px;
  color: #ffffff;
  cursor: pointer;
}

/* Sidebar styles */
.sidebar {
  width: 250px;
  background-color: #FAFAFA;
  padding: 20px;
  color: var(--text-color);
  height: 100vh;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.sidebar h2 {
  font-size: 20px;
  margin-bottom: 20px;
}

.sidebar ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.sidebar li {
  padding: 10px 0;
}

.sidebar li a, .sidebar li a._dash-link {
  color: var(--text-color);
  display: block;
  transition: color 0.3s ease;
}

.sidebar li a:hover, .sidebar li a._dash-link:hover {
  color: var(--background-color);
}

/* Layout styles */
.main-wrapper {
  display: flex;
  flex-direction: row;
}

/* Content styles */
.content {
  flex-grow: 1;
  padding: 20px;
  background-color: var(--background-color);
  min-height: calc(100vh - 60px);
}

/* Card styles */
.card {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

/* Grid layout */
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

/* Footer styles */
.footer {
  background-color: #FFFFFF;
  color: var(--text-color);
  padding: 20px;
  text-align: center;
  margin-top: auto;
}

.footer p {
  color: var(--text-color);
  margin: 0;
}

.footer a {
  color: #000000;
  font-weight: 600;
  transition: color 0.3s ease;
}

.footer a:hover {
  color: #F7636C;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .nav-btn, .nav-btn._dash-link {
    padding: 7px 12px;
    font-size: 12px;
    min-height: 34px;
  }

  .nav-btn i {
    margin-right: 6px;
    font-size: 12px;
  }

  .navbar-links {
    gap: 8px;
  }
}

@media (max-width: 992px) {
  .main-wrapper {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    display: none;
  }

  .sidebar.active {
    display: block;
  }

  .grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .nav-btn, .nav-btn._dash-link {
    padding: 6px 10px;
    font-size: 11px;
    min-height: 32px;
  }

  .navbar-links {
    gap: 6px;
  }
}

@media (max-width: 768px) {
  .navbar {
    padding: 10px 15px;
  }
  
  .navbar-links {
    display: none;
  }
  
  .navbar-links.active {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 60px;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.95);  /* Fundo branco semi-transparente */
    padding: 20px;
    gap: 15px;
    backdrop-filter: blur(10px);
  }
  
  .menu-toggle {
    display: block;
  }
  
  .content {
    padding: 15px;
  }
  
  .grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .navbar h1 {
    font-size: 20px;
  }
}

@media (max-width: 576px) {
  .navbar h1 {
    font-size: 18px;
  }

  .card {
    padding: 15px;
  }

  .footer {
    padding: 15px;
  }
}

/* assets/styles.css - Adições para o toggle do menu */

/* Estilos para os botões de toggle */
.menu-toggle, .sidebar-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 24px;
  color: #ffffff;
  cursor: pointer;
  padding: 10px;
}

.sidebar-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #1f2937;
  color: #ffffff;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  z-index: 999;
  transition: transform 0.3s ease;
}

.sidebar-toggle:hover {
  transform: scale(1.1);
}

/* Ajustes para a página principal */
.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Classe para manipular visibilidade do sidebar */
@media (max-width: 992px) {
  .menu-toggle {
    display: block;
  }
  
  .sidebar-toggle {
    display: flex;
  }
  
  .sidebar {
    position: fixed;
    left: -250px;
    top: 70px;
    bottom: 0;
    z-index: 100;
    transition: left 0.3s ease;
    height: calc(100vh - 70px);
  }
  
  .sidebar.active {
    left: 0;
  }
  
  .navbar-links {
    display: none;
  }
  
  .navbar-links.active {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 60px;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.95);
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    z-index: 999;
  }
  
  .navbar-links.active a, .navbar-links.active a._dash-link {
    padding: 10px 0;
  }
}

/* assets/styles.css - Adições para botões e ícones */

/* Estilos para botões na navbar - Otimizado para fundos coloridos */
.nav-btn, .nav-btn._dash-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 14px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 18px;
  transition: all 0.3s ease;
  color: #1f2937;
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
  text-decoration: none;
  min-height: 36px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(5px);
}

.nav-btn:hover, .nav-btn._dash-link:hover {
  background-color: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  color: #111827;
}

.nav-btn:hover i, .nav-btn._dash-link:hover i {
  color: #111827;
}

.nav-btn i {
  margin-right: 7px;
  font-size: 13px;
  color: #1f2937;
}

/* Estilos melhorados para a sidebar */
.sidebar-menu {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.sidebar-menu li {
  margin-bottom: -20px;
}

.sidebar-menu li a, .sidebar-menu li a._dash-link {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 4px;
  transition: all 0.3s ease;
  color: #222222;
  position: relative;
}

.sidebar-menu li a:hover, .sidebar-menu li a._dash-link:hover {
  background-color: rgba(116, 192, 252, 0.2);
  color: #222222;
}

/* Estado ativo do menu lateral */
.sidebar-menu li a.active, .sidebar-menu li a._dash-link.active {
  background-color: #e7f1ff;
  color: #2a6df4;
  font-weight: 600;
  border-left: 4px solid #2a6df4;
  padding-left: 6px;
}

.sidebar-menu li a.active i, .sidebar-menu li a._dash-link.active i {
  color: #2a6df4;
  font-weight: 900;
}

.sidebar-menu li a i {
  width: 24px;
  margin-right: 8px;
  text-align: center;
}

.sidebar-divider {
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: #666;
  margin-top: 20px;
  margin-bottom: 10px;
  padding-left: 10px;
  font-weight: 600;
}

/* Adicionando efeito de transição para conteúdo da página */
#dashboard1-content {
  transition: opacity 0.3s ease;
}

/* Ajustes para responsividade com os novos elementos */
@media (max-width: 768px) {
  .navbar-links.active {
    padding: 15px;
  }
  
  .nav-btn, .nav-btn._dash-link {
    display: flex;
    width: 100%;
    margin: 5px 0;
  }
  
  .sidebar-menu li a, .sidebar-menu li a._dash-link {
    padding: 12px 10px;
  }
}

/* Animações para ícones */
.sidebar-menu li a i,
.nav-btn i {
  transition: transform 0.3s ease, color 0.3s ease;
}

.sidebar-menu li a:hover i,
.nav-btn:hover i {
  transform: scale(1.2);
}

/* Estilo para animação de transição de página */
.dash-loading {
  opacity: 0.5;
}

/* Estilo para o botão "Voltar à Página Inicial" padronizado */
.back-to-home-btn:hover {
  background-color: #0056b3 !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3) !important;
}

/* Estilos específicos para a página inicial responsiva */
.responsive-container {
  transition: max-width 0.3s ease, padding 0.3s ease;
}

/* Ajustes para diferentes tamanhos de tela */
@media (min-width: 1600px) {
  .responsive-container {
    max-width: 1400px !important;
  }
}

@media (min-width: 1400px) and (max-width: 1599px) {
  .responsive-container {
    max-width: 95% !important;
  }
}

@media (max-width: 1200px) {
  .responsive-container {
    max-width: 94% !important;
    padding: 25px 12px !important;
  }
}

@media (max-width: 992px) {
  .responsive-container {
    max-width: 96% !important;
    padding: 20px 10px !important;
  }

  /* Ajuste dos cards para telas médias */
  .dashboard-card {
    width: 300px !important;
    min-width: 280px !important;
  }

  /* Ajuste do gap para telas médias */
  .cards-container {
    gap: 25px !important;
  }
}

@media (max-width: 768px) {
  .responsive-container {
    max-width: 100% !important;
    padding: 15px 5px !important;
  }

  /* Cards em coluna única para mobile */
  .dashboard-card {
    width: 100% !important;
    max-width: 350px !important;
    min-width: auto !important;
    margin: 0 auto !important;
  }

  /* Ajuste do gap para mobile */
  .cards-container {
    flex-direction: column !important;
    align-items: center !important;
    gap: 15px !important;
  }

  /* Títulos responsivos para tablets */
  .responsive-container h2 {
    font-size: 24px !important;
  }

  .responsive-container h4 {
    font-size: 20px !important;
  }
}

@media (max-width: 576px) {
  .responsive-container {
    padding: 10px 5px !important;
  }

  .dashboard-card {
    height: 200px !important;
    padding: 15px !important;
  }

  /* Títulos menores em mobile */
  .responsive-container h2 {
    font-size: 22px !important;
  }

  .responsive-container h4 {
    font-size: 18px !important;
  }
}