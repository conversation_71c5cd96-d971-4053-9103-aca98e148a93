/* Login Styles - GenomaSUS Fenótipos */

/* P<PERSON>gina de login */
.login-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #caf0f8 0%, #0096c7 50%, #023e8a 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    font-family: var(--font-family, 'Poppins', sans-serif);
}

/* Container principal */
.login-container {
    width: 100%;
    max-width: 400px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Card de login */
.login-card {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px 30px;
    width: 100%;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 7px;
    background: linear-gradient(90deg, #368608, #fcf578, #368608);
}

/* Logo */
.login-logo {
    margin-bottom: 22px;
}

.logo-image {
    max-width: 120px;
    height: auto;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Títulos */
.login-title {
    color: #292A2D;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    font-family: var(--font-family, 'Poppins', sans-serif);
}

.login-subtitle {
    color: #666;
    font-size: 14px;
    margin-bottom: 30px;
    line-height: 1.5;
}

/* Formulário */
.login-form {
    text-align: left;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    color: #292A2D;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    font-family: var(--font-family, 'Poppins', sans-serif);
    transition: all 0.3s ease;
    background: #fafafa;
}

.form-input:focus {
    outline: none;
    border-color: #105c75;
    background: #ffffff;
    box-shadow: 0 0 0 3px rgba(16, 92, 117, 0.1);
}

/* Container do checkbox "Exibir senha" */
.show-password-container {
    margin-top: 8px;
}

.show-password-checkbox {
    font-family: var(--font-family, 'Poppins', sans-serif);
}

.show-password-checkbox label {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #666;
    cursor: pointer;
    user-select: none;
}

.show-password-checkbox input[type="checkbox"] {
    margin-right: 8px;
    width: 16px;
    height: 16px;
    accent-color: #105c75;
    cursor: pointer;
}

.show-password-checkbox input[type="checkbox"]:focus {
    outline: 2px solid #105c75;
    outline-offset: 2px;
}

/* Botão de login */
.login-btn {
    width: 100%;
    padding: 12px 24px;
    background: linear-gradient(to right, #0096c7, #0077b6, #0b4188);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    font-family: var(--font-family, 'Poppins', sans-serif);
    cursor: pointer;
    transition: all 0.3s ease-in-out;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.login-btn:hover {
    filter: brightness(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.login-btn:active {
    transform: translateY(0);
}

/* Link esqueci a senha */
.forgot-password {
    text-align: center;
}

.forgot-link {
    color: #105c75;
    font-size: 14px;
    text-decoration: none;
    transition: color 0.3s ease;
}

.forgot-link:hover {
    color: #0e4e63;
    text-decoration: underline;
}

/* Mensagens */
.login-message {
    margin-bottom: 20px;
    min-height: 20px;
}

.error-message {
    background: #ffebee;
    color: #c62828;
    padding: 12px 16px;
    border-radius: 8px;
    border-left: 4px solid #c62828;
    font-size: 14px;
    margin-bottom: 10px;
    animation: slideIn 0.3s ease;
}

.success-message {
    background: #e8f5e8;
    color: #2e7d32;
    padding: 12px 16px;
    border-radius: 8px;
    border-left: 4px solid #2e7d32;
    font-size: 14px;
    margin-bottom: 10px;
    animation: slideIn 0.3s ease;
}

/* Rodapé */
.login-footer {
    margin-top: 30px;
    text-align: center;
}

.footer-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin: 0;
}

/* Animações */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsividade */
@media (max-width: 480px) {
    .login-page {
        padding: 15px;
    }
    
    .login-card {
        padding: 30px 20px;
    }
    
    .login-title {
        font-size: 20px;
    }
    
    .logo-image {
        max-width: 100px;
    }
}
