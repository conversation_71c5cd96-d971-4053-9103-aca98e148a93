#!/usr/bin/env python3
"""
Componentes visuais para harmonização de variáveis
Gráficos interativos comparativos entre estudos GS-009, GS-004 e GS-032
"""

import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
from .utils import get_harmonized_data_for_question

# Cores padronizadas para os estudos
STUDY_COLORS = {
    'GS-009': '#ADD8E6',  # Azul claro
    'GS-004': '#b7d5ac',  # Verde claro
    'GS-032': '#DDA0DD'   # Roxo claro
}

def create_age_distribution_chart(data_dict: Dict[str, pd.DataFrame]) -> go.Figure:
    """
    Cria gráfico de distribuição etária comparativo
    
    Args:
        data_dict (Dict[str, pd.DataFrame]): Dados por estudo
    
    Returns:
        go.Figure: Gráfico Plotly
    """
    fig = go.Figure()
    
    # Ordem das categorias etárias
    age_order = ['< 30', '30-39', '40-49', '50-59', '60-69', '70-79', '≥ 80']
    
    for study, data in data_dict.items():
        if data.empty:
            continue
        
        # Calcular distribuição percentual
        value_counts = data['categoria'].value_counts()
        total = len(data)
        percentages = (value_counts / total * 100).round(1)
        
        # Reordenar conforme age_order
        ordered_percentages = []
        ordered_categories = []
        for age_cat in age_order:
            if age_cat in percentages.index:
                ordered_percentages.append(percentages[age_cat])
                ordered_categories.append(age_cat)
        
        fig.add_trace(go.Bar(
            name=study,
            x=ordered_categories,
            y=ordered_percentages,
            marker_color=STUDY_COLORS[study],
            text=[f'{p}%' for p in ordered_percentages],
            textposition='outside',
            hovertemplate=f'<b>{study}</b><br>' +
                         'Faixa Etária: %{x}<br>' +
                         'Percentual: %{y}%<br>' +
                         '<extra></extra>'
        ))
    
    fig.update_layout(
        title={
            'text': 'Distribuição Etária dos Participantes por Estudo',
            'x': 0.5,
            'font': {'size': 18, 'family': 'Arial, sans-serif'}
        },
        xaxis_title='Faixa Etária',
        yaxis_title='Percentual (%)',
        barmode='group',
        template='plotly_white',
        height=500,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        margin=dict(t=80, b=60, l=60, r=60)
    )
    
    return fig

def create_sex_distribution_chart(data_dict: Dict[str, pd.DataFrame]) -> go.Figure:
    """
    Cria gráfico de distribuição por sexo comparativo
    
    Args:
        data_dict (Dict[str, pd.DataFrame]): Dados por estudo
    
    Returns:
        go.Figure: Gráfico Plotly
    """
    fig = go.Figure()
    
    sex_order = ['Feminino', 'Masculino']
    
    for study, data in data_dict.items():
        if data.empty:
            continue
        
        # Calcular distribuição percentual
        value_counts = data['categoria'].value_counts()
        total = len(data)
        percentages = (value_counts / total * 100).round(1)
        
        # Reordenar conforme sex_order
        ordered_percentages = []
        ordered_categories = []
        for sex_cat in sex_order:
            if sex_cat in percentages.index:
                ordered_percentages.append(percentages[sex_cat])
                ordered_categories.append(sex_cat)
        
        fig.add_trace(go.Bar(
            name=study,
            x=ordered_categories,
            y=ordered_percentages,
            marker_color=STUDY_COLORS[study],
            text=[f'{p}%' for p in ordered_percentages],
            textposition='outside',
            hovertemplate=f'<b>{study}</b><br>' +
                         'Sexo: %{x}<br>' +
                         'Percentual: %{y}%<br>' +
                         '<extra></extra>'
        ))
    
    fig.update_layout(
        title={
            'text': 'Distribuição por Sexo dos Participantes por Estudo',
            'x': 0.5,
            'font': {'size': 18, 'family': 'Arial, sans-serif'}
        },
        xaxis_title='Sexo',
        yaxis_title='Percentual (%)',
        barmode='group',
        template='plotly_white',
        height=500,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        margin=dict(t=80, b=60, l=60, r=60)
    )
    
    return fig

def create_race_distribution_chart(data_dict: Dict[str, pd.DataFrame]) -> go.Figure:
    """
    Cria gráfico de distribuição por raça/cor comparativo
    
    Args:
        data_dict (Dict[str, pd.DataFrame]): Dados por estudo
    
    Returns:
        go.Figure: Gráfico Plotly
    """
    fig = go.Figure()
    
    race_order = ['Branca', 'Parda', 'Preta', 'Amarela', 'Indígena']
    
    for study, data in data_dict.items():
        if data.empty:
            continue
        
        # Calcular distribuição percentual
        value_counts = data['categoria'].value_counts()
        total = len(data)
        percentages = (value_counts / total * 100).round(1)
        
        # Reordenar conforme race_order
        ordered_percentages = []
        ordered_categories = []
        for race_cat in race_order:
            if race_cat in percentages.index:
                ordered_percentages.append(percentages[race_cat])
                ordered_categories.append(race_cat)
        
        fig.add_trace(go.Bar(
            name=study,
            x=ordered_categories,
            y=ordered_percentages,
            marker_color=STUDY_COLORS[study],
            text=[f'{p}%' for p in ordered_percentages],
            textposition='outside',
            hovertemplate=f'<b>{study}</b><br>' +
                         'Raça/Cor: %{x}<br>' +
                         'Percentual: %{y}%<br>' +
                         '<extra></extra>'
        ))
    
    fig.update_layout(
        title={
            'text': 'Distribuição por Raça/Cor dos Participantes por Estudo',
            'x': 0.5,
            'font': {'size': 18, 'family': 'Arial, sans-serif'}
        },
        xaxis_title='Raça/Cor',
        yaxis_title='Percentual (%)',
        barmode='group',
        template='plotly_white',
        height=500,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        margin=dict(t=80, b=60, l=60, r=60)
    )
    
    return fig

def create_income_distribution_chart(data_dict: Dict[str, pd.DataFrame]) -> go.Figure:
    """
    Cria gráfico de distribuição de renda comparativo
    
    Args:
        data_dict (Dict[str, pd.DataFrame]): Dados por estudo
    
    Returns:
        go.Figure: Gráfico Plotly
    """
    fig = go.Figure()
    
    for study, data in data_dict.items():
        if data.empty:
            continue
        
        # Calcular distribuição percentual
        value_counts = data['categoria'].value_counts()
        total = len(data)
        percentages = (value_counts / total * 100).round(1)
        
        categories = list(value_counts.index)
        percentages_list = list(percentages.values)
        
        fig.add_trace(go.Bar(
            name=study,
            x=categories,
            y=percentages_list,
            marker_color=STUDY_COLORS[study],
            text=[f'{p}%' for p in percentages_list],
            textposition='outside',
            hovertemplate=f'<b>{study}</b><br>' +
                         'Categoria de Renda: %{x}<br>' +
                         'Percentual: %{y}%<br>' +
                         '<extra></extra>'
        ))
    
    fig.update_layout(
        title={
            'text': 'Distribuição de Renda dos Participantes por Estudo',
            'x': 0.5,
            'font': {'size': 18, 'family': 'Arial, sans-serif'}
        },
        xaxis_title='Categoria de Renda',
        yaxis_title='Percentual (%)',
        barmode='group',
        template='plotly_white',
        height=500,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        margin=dict(t=80, b=60, l=60, r=60)
    )
    
    return fig

def create_hypertension_prevalence_chart(data_dict: Dict[str, pd.DataFrame]) -> go.Figure:
    """
    Cria gráfico de prevalência de hipertensão comparativo
    
    Args:
        data_dict (Dict[str, pd.DataFrame]): Dados por estudo
    
    Returns:
        go.Figure: Gráfico Plotly
    """
    fig = go.Figure()
    
    prevalence_data = []
    studies = []
    
    for study, data in data_dict.items():
        if data.empty:
            continue
        
        # Calcular prevalência (% de "Sim")
        value_counts = data['categoria'].value_counts()
        total = len(data)
        
        if 'Sim' in value_counts.index:
            prevalence = (value_counts['Sim'] / total * 100).round(1)
        else:
            prevalence = 0.0
        
        prevalence_data.append(prevalence)
        studies.append(study)
    
    for i, study in enumerate(studies):
        fig.add_trace(go.Bar(
            name=study,
            x=[study],
            y=[prevalence_data[i]],
            marker_color=STUDY_COLORS[study],
            text=[f'{prevalence_data[i]}%'],
            textposition='outside',
            hovertemplate=f'<b>{study}</b><br>' +
                         'Prevalência de Hipertensão: %{y}%<br>' +
                         '<extra></extra>'
        ))

    fig.update_layout(
        title={
            'text': 'Prevalência de Hipertensão por Estudo',
            'x': 0.5,
            'font': {'size': 18, 'family': 'Arial, sans-serif'}
        },
        xaxis_title='Estudo',
        yaxis_title='Prevalência (%)',
        template='plotly_white',
        height=500,
        margin=dict(t=80, b=60, l=60, r=60),
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )
    
    return fig

def create_diabetes_prevalence_chart(data_dict: Dict[str, pd.DataFrame]) -> go.Figure:
    """
    Cria gráfico de prevalência de diabetes comparativo
    
    Args:
        data_dict (Dict[str, pd.DataFrame]): Dados por estudo
    
    Returns:
        go.Figure: Gráfico Plotly
    """
    fig = go.Figure()
    
    prevalence_data = []
    studies = []
    
    for study, data in data_dict.items():
        if data.empty:
            continue
        
        # Calcular prevalência (% de "Sim")
        value_counts = data['categoria'].value_counts()
        total = len(data)
        
        if 'Sim' in value_counts.index:
            prevalence = (value_counts['Sim'] / total * 100).round(1)
        else:
            prevalence = 0.0
        
        prevalence_data.append(prevalence)
        studies.append(study)
    
    for i, study in enumerate(studies):
        fig.add_trace(go.Bar(
            name=study,
            x=[study],
            y=[prevalence_data[i]],
            marker_color=STUDY_COLORS[study],
            text=[f'{prevalence_data[i]}%'],
            textposition='outside',
            hovertemplate=f'<b>{study}</b><br>' +
                         'Prevalência de Diabetes: %{y}%<br>' +
                         '<extra></extra>'
        ))

    fig.update_layout(
        title={
            'text': 'Prevalência de Diabetes por Estudo',
            'x': 0.5,
            'font': {'size': 18, 'family': 'Arial, sans-serif'}
        },
        xaxis_title='Estudo',
        yaxis_title='Prevalência (%)',
        template='plotly_white',
        height=500,
        margin=dict(t=80, b=60, l=60, r=60),
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )
    
    return fig
