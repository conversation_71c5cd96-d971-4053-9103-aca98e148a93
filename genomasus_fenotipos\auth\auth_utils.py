"""
Utilitários de autenticação para o sistema GenomaSUS Fenótipos.
"""

import hashlib
import re
import time
from datetime import datetime, timed<PERSON>ta

def hash_password(password):
    """
    Retorna o hash SHA-256 de uma senha.
    """
    return hashlib.sha256(password.encode('utf-8')).hexdigest()

def verify_password(password, password_hash):
    """
    Compara uma senha em texto com o hash armazenado.
    """
    return hash_password(password) == password_hash

def validate_username(username):
    """
    Valida o formato do nome de usuário.
    """
    if not username or len(username.strip()) == 0:
        return {'valid': False, 'message': 'Nome de usuário não pode estar vazio'}
    
    if len(username) < 3:
        return {'valid': False, 'message': 'Nome de usuário deve ter pelo menos 3 caracteres'}
    
    if len(username) > 50:
        return {'valid': False, 'message': 'Nome de usuário deve ter no máximo 50 caracteres'}
    
    if not re.match(r'^[a-zA-Z0-9._]+$', username):
        return {'valid': False, 'message': 'Nome de usuário pode conter apenas letras, números, pontos e underscores'}
    
    return {'valid': True, 'message': 'Nome de usuário válido'}

def validate_password(password):
    """
    Valida o formato da senha.
    """
    if not password or len(password.strip()) == 0:
        return {'valid': False, 'message': 'Senha não pode estar vazia'}
    
    if len(password) < 4:
        return {'valid': False, 'message': 'Senha deve ter pelo menos 4 caracteres'}
    
    if len(password) > 100:
        return {'valid': False, 'message': 'Senha deve ter no máximo 100 caracteres'}
    
    return {'valid': True, 'message': 'Senha válida'}

def validate_login_form(username, password):
    """
    Valida os dois campos do formulário de login.
    """
    if not username or not password or len(username.strip()) == 0 or len(password.strip()) == 0:
        return {
            'valid': False, 
            'message': 'Preencha todos os campos antes de continuar.',
            'field': 'both'
        }

    username_validation = validate_username(username)
    if not username_validation['valid']:
        return {
            'valid': False,
            'message': username_validation['message'],
            'field': 'username'
        }

    password_validation = validate_password(password)
    if not password_validation['valid']:
        return {
            'valid': False,
            'message': password_validation['message'],
            'field': 'password'
        }

    return {
        'valid': True,
        'message': 'Formulário válido',
        'field': 'none'
    }

def sanitize_input(input_string):
    """
    Remove espaços extras e limita o tamanho da string de entrada.
    """
    if not input_string:
        return ""

    return input_string.strip()[:100]

# Funções de gerenciamento de sessão
def create_session_data(user_data):
    """
    Cria dados de sessão para um usuário autenticado.

    Args:
        user_data (dict): Dados do usuário retornados pela autenticação

    Returns:
        dict: Dados da sessão incluindo timestamp de último acesso
    """
    if not user_data:
        return None

    # Sessão com controle de inatividade (30 minutos)
    current_time = datetime.now()

    return {
        'user_id': user_data['id'],
        'username': user_data['username'],
        'login_time': current_time.isoformat(),
        'last_access_timestamp': current_time.isoformat(),
        'is_authenticated': True
    }

def is_session_valid(session_data):
    """
    Verifica se uma sessão é válida e não ultrapassou o timeout de inatividade (30 minutos).

    Args:
        session_data (dict): Dados da sessão

    Returns:
        bool: True se a sessão é válida, False caso contrário
    """
    if not session_data or not isinstance(session_data, dict):
        return False

    if not session_data.get('is_authenticated'):
        return False

    if not session_data.get('last_access_timestamp'):
        return False

    try:
        last_access = datetime.fromisoformat(session_data['last_access_timestamp'])
        current_time = datetime.now()
        # Timeout de inatividade: 30 minutos
        return (current_time - last_access) < timedelta(minutes=30)
    except (ValueError, TypeError):
        return False

def clear_session():
    """
    Retorna dados de sessão limpos/vazios.

    Returns:
        dict: Sessão vazia
    """
    return {
        'user_id': None,
        'username': None,
        'login_time': None,
        'last_access_timestamp': None,
        'is_authenticated': False
    }

def update_session_activity(session_data):
    """
    Atualiza o timestamp de última atividade de uma sessão válida.

    Args:
        session_data (dict): Dados da sessão atual

    Returns:
        dict: Dados da sessão com timestamp atualizado
    """
    if not is_session_valid(session_data):
        return clear_session()

    # Cria uma cópia da sessão e atualiza o timestamp de última atividade
    updated_session = session_data.copy()
    updated_session['last_access_timestamp'] = datetime.now().isoformat()

    return updated_session
