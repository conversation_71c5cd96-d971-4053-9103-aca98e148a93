"""
Tela de login do GenomaSUS Fenótipos.
Layout responsivo e callbacks de autenticação.
"""

from dash import html, dcc, Input, Output, State
import dash
from .database import authenticate_user, init_database
from .auth_utils import validate_login_form, sanitize_input, create_session_data

# Inicializa o banco de dados local (SQLite)
init_database()

# Layout principal da tela de login
layout = html.Div(
    id='login-page',
    className='login-page',
    children=[
        html.Div(
            className='login-container',
            children=[
                html.Div(
                    className='login-card',
                    children=[
                        html.Div(
                            className='login-logo',
                            children=[
                                html.Img(
                                    src='/assets/images/logo_genomasus.png',
                                    alt='GenomaSUS Logo',
                                    className='logo-image'
                                )
                            ]
                        ),
                        html.H1('GenomaSUS Fenótipos', className='login-title'),
                        html.P('Faça login para acessar os dashboards do GenomaSUS.', className='login-subtitle'),

                        html.Div(id='login-message', className='login-message'),

                        html.Div(
                            className='login-form',
                            children=[
                                html.Div(
                                    className='form-group',
                                    children=[
                                        html.Label('Usuário:', className='form-label'),
                                        dcc.Input(
                                            id='login-username',
                                            type='text',
                                            placeholder='Digite seu usuário',
                                            className='form-input',
                                            value='',
                                            maxLength=50
                                        )
                                    ]
                                ),
                                html.Div(
                                    className='form-group',
                                    children=[
                                        html.Label('Senha:', className='form-label'),
                                        dcc.Input(
                                            id='login-password',
                                            type='password',
                                            placeholder='Digite sua senha',
                                            className='form-input',
                                            value='',
                                            maxLength=100
                                        ),
                                        html.Div(
                                            className='show-password-container',
                                            children=[
                                                dcc.Checklist(
                                                    id='show-password-checkbox',
                                                    options=[{'label': 'Exibir senha', 'value': 'show'}],
                                                    value=[],
                                                    className='show-password-checkbox'
                                                )
                                            ]
                                        )
                                    ]
                                ),
                                html.Button(
                                    'Entrar',
                                    id='login-button',
                                    className='login-btn',
                                    n_clicks=0
                                ),
                                html.Div(
                                    className='forgot-password',
                                    children=[
                                        html.A(
                                            'Esqueci a senha',
                                            href='#',
                                            className='forgot-link'
                                        )
                                    ]
                                )
                            ]
                        )
                    ]
                )
            ]
        ),
        html.Footer(
            className='login-footer',
            children=[
                html.P('Equipe Fenótipos | GenomaSUS 2025', className='footer-text')
            ]
        ),
        dcc.Store(id='login-redirect', data=False),
        dcc.Store(id='session-store', data={'is_authenticated': False}, storage_type='session'),
        dcc.Location(id='login-url', refresh=True)
    ]
)


def register_callbacks(app):
    """
    Registra os callbacks responsáveis pela lógica de exibição da senha,
    validação do formulário e redirecionamento pós-login.
    """

    @app.callback(
        Output('login-password', 'type'),
        Input('show-password-checkbox', 'value'),
        prevent_initial_call=True
    )
    def toggle_password_visibility(checkbox_value):
        """Alterna a visualização da senha (oculta ⇄ texto)."""
        return 'text' if 'show' in checkbox_value else 'password'

    @app.callback(
        Output('login-message', 'children'),
        Output('login-redirect', 'data'),
        Output('session-store', 'data'),
        Input('login-button', 'n_clicks'),
        State('login-username', 'value'),
        State('login-password', 'value'),
        prevent_initial_call=True
    )
    def handle_login(n_clicks, username, password):
        """Processa a tentativa de login com validações e autenticação."""
        if n_clicks == 0:
            return [], False, {'is_authenticated': False}

        username = sanitize_input(username or "")
        password = sanitize_input(password or "")

        validation = validate_login_form(username, password)
        if not validation['valid']:
            return [html.Div(validation['message'], className='error-message')], False, {'is_authenticated': False}

        user_data = authenticate_user(username, password)
        if user_data:
            session_data = create_session_data(user_data)
            return [html.Div('Login realizado com sucesso.', className='success-message')], True, session_data
        else:
            return [html.Div('Usuário ou senha inválidos.', className='error-message')], False, {'is_authenticated': False}

    @app.callback(
        Output('login-url', 'pathname'),
        Input('login-redirect', 'data'),
        prevent_initial_call=True
    )
    def redirect_after_login(should_redirect):
        """Redireciona para a página inicial se o login for bem-sucedido."""
        return '/' if should_redirect else '/login'
