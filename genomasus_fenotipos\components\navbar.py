#!/usr/bin/env python3

from dash import html, dcc
import sys
import os

# Adicionar o diretório raiz ao path para importar config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.dashboard_colors import get_dashboard_info, generate_navbar_style

# Configurações dos dashboards
DASHBOARD_CONFIG = {
    "dashboard1": {
        "title": ["SABE", "Saúde, Bem-estar e Envelhecimento"],
        "button_text": " SABE Inicial",
        "button_href": "/dashboard1/sabe-sociodemografico",
        "button_icon": "fa fa-chart-bar"
    },
    "dashboard2": {
        "title": ["Renomica-DAC", "Rede Nacional de Genômica Cardiovascular – Doença Arterial Coronariana"],
        "button_text": " Renomica-DAC Inicial",
        "button_href": "/dashboard2/renomica-sociodemografico",
        "button_icon": "fa fa-chart-line"
    },
    "dashboard3": {
        "title": ["EPIGEN-II-PGx-cardiovascular", "Farmacogenética"],
        "button_text": " EPIGEN-II-PGx Inicial",
        "button_href": "/dashboard3/farmacogenetica-sociodemografico",
        "button_icon": "fa fa-dna"
    },
    "harmonizacao": {
        "title": ["Harmonização de Variáveis", "Comparação entre estudos GenomaSUS"],
        "button_text": " Sobre Harmonização",
        "button_href": "/harmonizacao/sobre",
        "button_icon": "fa fa-layer-group"
    },
    "contato": {
        "title": "Suporte e Contato",
        "button_text": " Página Inicial",
        "button_href": "/",
        "button_icon": "fa fa-home"
    },
    "default": {
        "title": "Configurações",
        "button_text": " Dashboards",
        "button_href": "/",
        "button_icon": "fa fa-chart-bar"
    }
}

# Links de navegação padrão
NAVIGATION_LINKS = [
    {"href": "/", "icon": "fa fa-home", "text": " Página Inicial"},
    {"href": "/sobre", "icon": "fa fa-info-circle", "text": " Sobre o Projeto GenomaSUS"},
    {"href": "/sistema/configuracoes", "icon": "fa fa-cog", "text": " Configurações"},
    {"href": "/sistema/contato", "icon": "fa fa-question-circle", "text": " Contato"}
]

def _create_navigation_links(dashboard_button_href, dashboard_button_icon, dashboard_button_text, dashboard_type=None):
    """Cria os links de navegação dinamicamente"""
    links = []

    # Link da página inicial
    links.append(dcc.Link(
        className='nav-btn',
        href=NAVIGATION_LINKS[0]["href"],
        children=[
            html.I(className=NAVIGATION_LINKS[0]["icon"], style={"margin-right": "12px"}),
            NAVIGATION_LINKS[0]["text"]
        ]
    ))

    # Para harmonização, adicionar botão "Sobre Harmonização" após "Página Inicial"
    if dashboard_type == "harmonizacao":
        links.append(dcc.Link(
            className='nav-btn',
            href="/harmonizacao/sobre",
            children=[
                html.I(className="fa fa-layer-group", style={"margin-right": "12px"}),
                " Sobre Harmonização"
            ]
        ))

    # Link específico do dashboard (se não for harmonização)
    if dashboard_type != "harmonizacao":
        links.append(dcc.Link(
            className='nav-btn',
            href=dashboard_button_href,
            children=[
                html.I(className=dashboard_button_icon, style={"margin-right": "12px"}),
                dashboard_button_text
            ]
        ))

    # Links restantes
    for link_config in NAVIGATION_LINKS[1:]:
        links.append(dcc.Link(
            className='nav-btn',
            href=link_config["href"],
            children=[
                html.I(className=link_config["icon"], style={"margin-right": "12px"}),
                link_config["text"]
            ]
        ))

    # Botão de logout
    links.append(html.Button(
        id='logout-button',
        className='nav-btn logout-btn',
        children=[
            html.I(className="fa fa-sign-out-alt", style={"margin-right": "8px"}),
            " Sair"
        ],
        style={
            'background': 'none',
            'border': 'none',
            'color': 'inherit',
            'cursor': 'pointer',
            'padding': '6px 12px',
            'border-radius': '4px',
            'transition': 'background-color 0.2s',
            'font-size': '12px',
            'display': 'flex',
            'align-items': 'center'
        }
    ))

    return links

def create_navbar(dashboard_type="default", current_route=None):
    # Obter configurações do dashboard
    config = DASHBOARD_CONFIG.get(dashboard_type, DASHBOARD_CONFIG["default"])
    navbar_style = generate_navbar_style(current_route or dashboard_type)

    # Extrair configurações
    title = config["title"]
    dashboard_button_text = config["button_text"]
    dashboard_button_href = config["button_href"]
    dashboard_button_icon = config["button_icon"]

    return html.Nav(
        className='navbar',
        style=navbar_style,
        children=[
            # Botão de toggle para o menu mobile
            html.Button(
                className='menu-toggle',
                id='navbar-toggle',
                children=html.I(className='fa fa-bars'),
            ),

            # Logo e título
            html.Div(
                style={'display': 'flex', 'align-items': 'center'},
                children=[
                    dcc.Link(
                        href='/',
                        children=[
                            html.Img(
                                src='/assets/images/logo_genomasus.png',
                                alt='Logo GenomasSUS',
                                style={'height': '60px', 'margin-right': '16px', 'cursor': 'pointer'},
                            ),
                        ],
                        style={'text-decoration': 'none'}
                    ),
                    html.H1(
                        style={'font-size': '20px', 'margin': '0', 'color': '#1f2937'},
                        children=[
                            html.Div(title[0], style={'font-weight': '600', 'line-height': '1.1', 'margin-bottom': '4px', 'font-size': '20px', 'color': '#1f2937'}) if isinstance(title, list) else title,
                            html.Div(title[1], style={'font-size': '14px', 'font-weight': '400', 'line-height': '1.2', 'margin-top': '0px', 'color': '#4b5563'}) if isinstance(title, list) and len(title) > 1 else None
                        ] if isinstance(title, list) else title
                    ),
                ],
            ),

            # Links de navegação como botões
            html.Div(
                className='navbar-links',
                id='navbar-links',
                children=_create_navigation_links(dashboard_button_href, dashboard_button_icon, dashboard_button_text, dashboard_type)
            ),

            # Botão de toggle para o sidebar
            html.Button(
                className='sidebar-toggle',
                id='sidebar-toggle',
                children=html.I(className='fa fa-sliders'),
            ),
        ],
    )

# Funções utilitárias para criar navbars com cores específicas
def create_navbar_for_route(route):
    # Determinar tipo de dashboard baseado na rota
    if route.startswith('/dashboard1'):
        dashboard_type = "dashboard1"
    elif route.startswith('/dashboard2'):
        dashboard_type = "dashboard2"
    elif route.startswith('/dashboard3'):
        dashboard_type = "dashboard3"
    elif route.startswith('/harmonizacao'):
        dashboard_type = "harmonizacao"
    elif route == '/sistema/contato':
        dashboard_type = "contato"
    else:
        dashboard_type = "default"

    return create_navbar(dashboard_type, current_route=route)

# Navbar padrão para compatibilidade com código existente
navbar = create_navbar("default")