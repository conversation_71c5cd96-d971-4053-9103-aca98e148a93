window.addEventListener('DOMContentLoaded', () => {
    
    // Ajusta visibilidade da sidebar e navbar conforme o tamanho da tela
    function adjustForScreenSize() {
        const sidebar = document.getElementById('sidebar');
        const navbarLinks = document.getElementById('navbar-links');
        
        if (sidebar && navbarLinks) {
            // Esconde sidebar em telas menores
            if (window.innerWidth <= 992) {
                sidebar.classList.remove('active');
            } else {
                sidebar.classList.add('active');
            }

            // Esconde menu de navegação em celulares
            if (window.innerWidth <= 768) {
                navbarLinks.classList.remove('active');
            }
        }
    }

    // Fecha menus ao clicar em um link
    function setupLinkListeners() {
        document.querySelectorAll('.navbar-links a, .sidebar a').forEach(link => {
            link.addEventListener('click', () => {
                const navbarLinks = document.getElementById('navbar-links');
                const sidebar = document.getElementById('sidebar');

                if (window.innerWidth <= 768 && navbarLinks) {
                    navbarLinks.classList.remove('active');
                }

                if (window.innerWidth <= 992 && sidebar) {
                    sidebar.classList.remove('active');
                }
            });
        });
    }

    // Fecha menus ao clicar fora deles
    function setupClickOutsideListener() {
        document.addEventListener('click', (event) => {
            const navbar = document.querySelector('.navbar');
            const sidebar = document.getElementById('sidebar');
            const navbarToggle = document.getElementById('navbar-toggle');
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const navbarLinks = document.getElementById('navbar-links');

            if (
                navbarLinks?.classList.contains('active') &&
                !navbar?.contains(event.target) &&
                event.target !== navbarToggle
            ) {
                navbarLinks.classList.remove('active');
            }

            if (
                sidebar?.classList.contains('active') &&
                !sidebar.contains(event.target) &&
                event.target !== sidebarToggle
            ) {
                sidebar.classList.remove('active');
            }
        });
    }

    // Execução inicial e após tempo para garantir que o Dash renderizou
    adjustForScreenSize();
    window.addEventListener('resize', adjustForScreenSize);

    setTimeout(() => {
        setupLinkListeners();
        setupClickOutsideListener();
        adjustForScreenSize();
    }, 500);
});
