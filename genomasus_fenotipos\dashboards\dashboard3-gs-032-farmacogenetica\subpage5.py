import pandas as pd
from dash import html, dcc, Input, Output
from components.navbar import create_navbar_for_route
from components.dynamic_sidebar import create_sidebar_for_route
from components.footer import footer
import json
import os
import re
from collections import Counter
from . import DATA_FILE

def get_current_palette():
    # Paletas de cores definidas
    COLOR_PALETTES = {
        "default": ["#6200EA", "#BB86FC", "#03DAC6", "#018786", "#FFEB3B", "#FFC107", "#FF9800", "#F44336", "#E91E63", "#9C27B0", "#673AB7", "#3F51B5"],
        "colorblind": ["#000000", "#E69F00", "#56B4E9", "#009E73", "#F0E442", "#0072B2", "#D55E00", "#CC79A7", "#999999", "#332288", "#44AA99", "#882255"],
        "autistic": ["#7F95D1", "#7FC9D1", "#7FD195", "#D1C97F", "#D17F95", "#957FD1", "#D1957F", "#95D17F", "#7F95D1", "#7FBCD1", "#D17F95", "#957FD1"],
        "monochrome": ["#0D47A1", "#1565C0", "#1976D2", "#1E88E5", "#2196F3", "#42A5F5", "#64B5F6", "#90CAF9", "#BBDEFB", "#E3F2FD", "#2962FF", "#0D47A1"],
        "pastel": ["#FFB6C1", "#FFD700", "#98FB98", "#ADD8E6", "#FFA07A", "#DDA0DD", "#87CEFA", "#F0E68C", "#E6E6FA", "#FFDAB9", "#B0E0E6", "#FFDEAD"]
    }
    
    config_file = 'config/app_config.json'
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        palette_key = config.get('color_palette', 'default')
    else:
        palette_key = 'default'
    
    return COLOR_PALETTES.get(palette_key, COLOR_PALETTES['default'])

# Carregamento e processamento dos dados
df = pd.read_csv(DATA_FILE)

def create_graph(title, figure, palette_colors=None):
    if palette_colors is None:
        palette_colors = get_current_palette()
    
    if 'data' in figure and figure['data']:
        for i, trace in enumerate(figure['data']):
            if 'marker' in trace:
                if 'color' in trace['marker'] and isinstance(trace['marker']['color'], str):
                    color_index = i % len(palette_colors)
                    trace['marker']['color'] = palette_colors[color_index]
                elif 'colors' in trace['marker'] and isinstance(trace['marker']['colors'], list):
                    num_colors = len(trace['marker']['colors'])
                    trace['marker']['colors'] = palette_colors[:num_colors]
    
    return html.Div([
        html.H3(title, style={'textAlign': 'center'}),
        dcc.Graph(figure=figure)
    ], className='graph-container')

def extract_medications(medication_text):
    """Extrai medicamentos individuais do texto de medicação"""
    if pd.isna(medication_text) or medication_text == 'FALSE':
        return []
    
    # Separar por / e limpar
    medications = []
    parts = str(medication_text).split('/')
    
    for part in parts:
        # Extrair apenas o nome do medicamento (antes do primeiro hífen ou espaço com dosagem)
        med_name = part.strip().split('-')[0].strip().split(' ')[0].strip()
        if med_name and len(med_name) > 2:  # Filtrar nomes muito curtos
            medications.append(med_name.upper())
    
    return medications

def extract_comorbidities(comorbidity_text):
    """Extrai comorbidades individuais do texto"""
    if pd.isna(comorbidity_text) or comorbidity_text == 'NAO' or comorbidity_text == 'NAO RELATA':
        return []

    # Separar por vírgulas e limpar
    comorbidities = []
    parts = str(comorbidity_text).split(',')

    for part in parts:
        comorbidity = part.strip().upper()
        # Excluir valores que indicam ausência de comorbidades
        if (comorbidity and len(comorbidity) > 2 and
            comorbidity not in ['NAO RELATA', 'MISSING', 'NAO']):
            comorbidities.append(comorbidity)

    return comorbidities

# Layout da subpágina de Medicamentos e Comorbidades
content = html.Div([
    html.H1("Medicamentos e Comorbidades - Farmacogenética (GS-032)", style={'textAlign': 'center'}),
    

    
    # Filtros globais
    html.Div([
        html.Label("Filtrar por Sexo:", style={'marginRight': '10px'}),
        dcc.Dropdown(
            id='sexo-filter-subpage5-dashboard3',
            options=[{'label': 'Todos', 'value': 'all'}] + 
                    [{'label': 'Feminino', 'value': 1}, {'label': 'Masculino', 'value': 0}],
            value='all',
            clearable=False,
            style={'width': '200px'}
        ),
        html.Label("Filtrar por Raça/Cor:", style={'marginLeft': '20px', 'marginRight': '10px'}),
        dcc.Dropdown(
            id='raca-filter-subpage5-dashboard3',
            options=[{'label': 'Todos', 'value': 'all'}] +
                    [{'label': 'Branco', 'value': 'ethnicity_race_white'},
                     {'label': 'Pardo', 'value': 'ethnicity_race_pardo'},
                     {'label': 'Preto', 'value': 'ethnicity_race_black'},
                     {'label': 'Amarelo', 'value': 'ethnicity_race_yellow'},
                     {'label': 'Indígena', 'value': 'ethnicity_race_indigenous'}],
            value='all',
            clearable=False,
            style={'width': '200px'}
        )
    ], style={'display': 'flex', 'justifyContent': 'center', 'marginBottom': '20px'}),
    
    # Gráficos base (serão atualizados dinamicamente)
    html.Div(id='dynamic-graphs-subpage5-dashboard3')
], className='content')

# Layout principal com a estrutura comum
layout = html.Div(
    className='page-container',
    children=[
        create_navbar_for_route("/dashboard3/farmacogenetica-medicamentos-comorbidades"),
        html.Div([
            create_sidebar_for_route("/dashboard3/farmacogenetica-medicamentos-comorbidades"),
            content,  
        ], className='main-section', style={'display': 'flex'}),
        footer,  
    ],
)

# Callbacks
def register_callbacks(app):
    @app.callback(
        Output('dynamic-graphs-subpage5-dashboard3', 'children'),
        [Input('sexo-filter-subpage5-dashboard3', 'value'), Input('raca-filter-subpage5-dashboard3', 'value')]
    )
    def update_graphs(sexo_filter, raca_filter):
        palette_colors = get_current_palette()
        
        filtered_df = df.copy()
        if sexo_filter != 'all':
            filtered_df = filtered_df[filtered_df['sex_feminino'] == sexo_filter]
        
        # Aplicar filtro de raça/cor
        if raca_filter != 'all':
            filtered_df = filtered_df[filtered_df[raca_filter] == 1]

        # Pergunta 37: Medicamentos mais comumente usados
        all_medications = []
        
        # Extrair medicamentos de todos os participantes que usam medicação
        medication_users = filtered_df[filtered_df['uses_regular_medication'] == True]
        
        for _, row in medication_users.iterrows():
            if 'medication_details' in row and pd.notna(row['medication_details']):
                meds = extract_medications(row['medication_details'])
                all_medications.extend(meds)
        
        # Contar medicamentos mais comuns
        med_counter = Counter(all_medications)
        top_medications = med_counter.most_common(15)  # Top 15 medicamentos
        
        if top_medications:
            med_names = [med[0] for med in top_medications]
            med_counts = [med[1] for med in top_medications]
            
            graph_1 = create_graph("Medicamentos Mais Comumente Usados", {
                'data': [{
                    'x': med_names,
                    'y': med_counts,
                    'type': 'bar',
                    'marker': {'color': palette_colors[0]}
                }],
                'layout': {
                    'title': 'Top 15 Medicamentos Mais Utilizados',
                    'xaxis': {
                        'title': 'Medicamento',
                        'tickangle': -60,
                        'tickfont': {'size': 10},
                        'automargin': True
                    },
                    'yaxis': {'title': 'Número de usuários'},
                    'height': 500,
                    'margin': {'b': 150}
                }
            }, palette_colors)
        else:
            graph_1 = create_graph("Medicamentos Mais Comumente Usados", {
                'data': [],
                'layout': {'title': 'Nenhum dado de medicamento disponível'}
            }, palette_colors)

        # Pergunta 38: Comorbidades mais comumente observadas
        all_comorbidities = []

        # Extrair comorbidades de todos os participantes
        for _, row in filtered_df.iterrows():
            if 'comorbidities' in row and pd.notna(row['comorbidities']):
                comorbidities = extract_comorbidities(row['comorbidities'])
                all_comorbidities.extend(comorbidities)

        # Contar comorbidades mais comuns
        comorbidity_counter = Counter(all_comorbidities)
        top_comorbidities = comorbidity_counter.most_common(10)  # Top 10 comorbidades

        if top_comorbidities:
            comorbidity_names = [comorbidity[0] for comorbidity in top_comorbidities]
            comorbidity_counts = [comorbidity[1] for comorbidity in top_comorbidities]

            graph_2 = create_graph("Comorbidades Mais Comumente Observadas", {
                'data': [{
                    'x': comorbidity_names,
                    'y': comorbidity_counts,
                    'type': 'bar',
                    'marker': {'color': palette_colors[1]}
                }],
                'layout': {
                    'title': 'Top 10 Comorbidades Mais Frequentes',
                    'xaxis': {
                        'title': 'Comorbidade',
                        'tickangle': -60,
                        'tickfont': {'size': 10},
                        'automargin': True
                    },
                    'yaxis': {'title': 'Número de casos'},
                    'height': 500,
                    'margin': {'b': 150}
                }
            }, palette_colors)
        else:
            graph_2 = create_graph("Comorbidades Mais Comumente Observadas", {
                'data': [],
                'layout': {'title': 'Nenhum dado de comorbidade disponível'}
            }, palette_colors)

        # Uso de medicação regular vs não regular
        med_usage_counts = filtered_df['uses_regular_medication'].value_counts()
        med_usage_labels = ['Não usa medicação regular', 'Usa medicação regular']
        med_usage_values = [med_usage_counts.get(False, 0), med_usage_counts.get(True, 0)]

        graph_3 = create_graph("Distribuição do Uso de Medicação Regular", {
            'data': [{
                'labels': med_usage_labels,
                'values': med_usage_values,
                'type': 'pie',
                'marker': {'colors': [palette_colors[2], palette_colors[3]]}
            }],
            'layout': {'title': 'Uso de Medicação Regular entre os Participantes'}
        }, palette_colors)

        # Análise de classes de medicamentos
        medication_classes = {
            'ANTI-HIPERTENSIVOS': ['LOSARTANA', 'ENALAPRIL', 'CAPTOPRIL', 'ANLODIPINO', 'ATENOLOL', 'METOPROLOL', 'CARVEDILOL', 'VALSARTANA'],
            'ANTIDIABÉTICOS': ['METFORMINA', 'GLIFAGE', 'INSULINA', 'GLICAZIDA', 'GLICLAZIDA', 'DAPAGLIFLOZINA'],
            'ANTICOAGULANTES': ['CLOPIDOGREL', 'VARFARINA', 'RIVAROXABANA', 'APIXABANA'],
            'ESTATINAS': ['SINVASTATINA', 'ATORVASTATINA', 'ROSUVASTATINA'],
            'DIURÉTICOS': ['HIDROCLOROTIAZIDA', 'FUROSEMIDA', 'ESPIRONOLACTONA']
        }

        class_counts = {}
        for class_name, drugs in medication_classes.items():
            count = 0
            for drug in drugs:
                count += all_medications.count(drug)
            if count > 0:
                class_counts[class_name] = count

        if class_counts:
            graph_4 = create_graph("Distribuição por Classes de Medicamentos", {
                'data': [{
                    'labels': list(class_counts.keys()),
                    'values': list(class_counts.values()),
                    'type': 'pie',
                    'marker': {'colors': palette_colors[:len(class_counts)]}
                }],
                'layout': {'title': 'Uso por Classes Terapêuticas'}
            }, palette_colors)
        else:
            graph_4 = create_graph("Distribuição por Classes de Medicamentos", {
                'data': [],
                'layout': {'title': 'Nenhum dado de classe de medicamento disponível'}
            }, palette_colors)

        # Número médio de medicamentos por participante
        med_per_participant = []
        for _, row in medication_users.iterrows():
            if 'medication_details' in row and pd.notna(row['medication_details']):
                meds = extract_medications(row['medication_details'])
                med_per_participant.append(len(meds))

        if med_per_participant:
            avg_meds = sum(med_per_participant) / len(med_per_participant)

            graph_5 = create_graph("Distribuição do Número de Medicamentos por Participante", {
                'data': [{
                    'x': med_per_participant,
                    'type': 'histogram',
                    'marker': {'color': palette_colors[4]},
                    'nbinsx': 10
                }],
                'layout': {
                    'title': f'Número de Medicamentos por Participante (Média: {avg_meds:.1f})',
                    'xaxis': {'title': 'Número de medicamentos'},
                    'yaxis': {'title': 'Número de participantes'}
                }
            }, palette_colors)
        else:
            graph_5 = create_graph("Distribuição do Número de Medicamentos por Participante", {
                'data': [],
                'layout': {'title': 'Nenhum dado disponível'}
            }, palette_colors)

        return html.Div([
            html.Div([graph_1, graph_2], className='row'),
            html.Div([graph_3, graph_4], className='row'),
            html.Div([graph_5], className='row')
        ])
