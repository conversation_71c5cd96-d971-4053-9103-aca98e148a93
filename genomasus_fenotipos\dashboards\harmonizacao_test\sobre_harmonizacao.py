#!/usr/bin/env python3
"""
Página "Sobre a Harmonização de Variáveis"
Informações sobre o processo de harmonização entre estudos GenomaSUS
"""

from dash import html, dcc
import sys
import os

# Adicionar o diretório raiz ao path para importar config
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from components.navbar import create_navbar_for_route
from components.footer import footer

# Layout da página
layout = html.Div(
    className='page-container',
    children=[
        html.Link(
            rel='stylesheet',
            href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
        ),
        
        create_navbar_for_route("/harmonizacao/sobre"),
        
        html.Div(
            className='main-wrapper',
            id='main-wrapper',
            children=[
                html.Div(
                    className='content',
                    style={
                        'background-color': '#f5f7fa',
                        'min-height': '100vh',
                        'padding': '20px'
                    },
                    children=[
                        # Container principal
                        html.Div(
                            style={
                                'max-width': '1000px',
                                'margin': '0 auto',
                                'background-color': '#ffffff',
                                'border-radius': '12px',
                                'box-shadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                                'padding': '40px'
                            },
                            children=[
                                html.H1(
                                    "Sobre a Harmonização de Variáveis",
                                    style={
                                        "textAlign": "center",
                                        "marginBottom": "30px",
                                        "color": "#333",
                                        "font-weight": "600"
                                    }
                                ),
                                
                                html.P(
                                    "Esta seção apresenta a proposta de harmonização entre os estudos participantes do GenomaSUS. "
                                    "A harmonização permite identificar e padronizar variáveis comuns, facilitando análises comparativas "
                                    "e integradas entre os diferentes bancos de dados fenotípicos.",
                                    style={
                                        "fontSize": "16px",
                                        "lineHeight": "1.6",
                                        "marginBottom": "20px",
                                        "color": "#555",
                                        "textAlign": "justify"
                                    }
                                ),
                                
                                html.P(
                                    "Essa etapa é essencial para garantir a reprodutibilidade e a interoperabilidade dos dados "
                                    "em estudos multicêntricos.",
                                    style={
                                        "fontSize": "16px",
                                        "lineHeight": "1.6",
                                        "marginBottom": "30px",
                                        "color": "#555",
                                        "textAlign": "justify"
                                    }
                                ),
                                
                                html.Hr(style={"margin": "30px 0", "border": "1px solid #eee"}),
                                
                                html.H3(
                                    "Estudos Participantes",
                                    style={
                                        "color": "#333",
                                        "marginBottom": "20px",
                                        "font-weight": "600"
                                    }
                                ),
                                
                                html.P(
                                    "Nesta área, serão inseridas as comparações, tabelas e visualizações de variáveis harmonizadas "
                                    "entre os estudos:",
                                    style={
                                        "fontSize": "16px",
                                        "lineHeight": "1.6",
                                        "marginBottom": "20px",
                                        "color": "#555"
                                    }
                                ),
                                
                                html.Ul([
                                    html.Li([
                                        html.Strong("GS-009 (SABE)"),
                                        " - Saúde, Bem-estar e Envelhecimento"
                                    ], style={"marginBottom": "10px", "fontSize": "16px", "color": "#555"}),
                                    html.Li([
                                        html.Strong("GS-004 (Renomica-DAC)"),
                                        " - Rede Nacional de Genômica Cardiovascular – Doença Arterial Coronariana"
                                    ], style={"marginBottom": "10px", "fontSize": "16px", "color": "#555"}),
                                    html.Li([
                                        html.Strong("GS-032 (Farmacogenética)"),
                                        " - EPIGEN-II-PGx-cardiovascular"
                                    ], style={"marginBottom": "10px", "fontSize": "16px", "color": "#555"})
                                ], style={"paddingLeft": "20px"}),
                                
                                html.Hr(style={"margin": "30px 0", "border": "1px solid #eee"}),
                                
                                html.H3(
                                    "Acesso às Comparações",
                                    style={
                                        "color": "#333",
                                        "marginBottom": "20px",
                                        "font-weight": "600"
                                    }
                                ),
                                
                                html.P(
                                    "Para visualizar as comparações detalhadas entre os estudos, acesse:",
                                    style={
                                        "fontSize": "16px",
                                        "lineHeight": "1.6",
                                        "marginBottom": "20px",
                                        "color": "#555"
                                    }
                                ),
                                
                                html.Div(
                                    style={"textAlign": "center", "marginTop": "30px"},
                                    children=[
                                        dcc.Link(
                                            "Visualizar Comparações de Harmonização",
                                            href="/harmonizacao/comparativo",
                                            style={
                                                "background-color": "#F9E79F",
                                                "color": "#333",
                                                "padding": "12px 24px",
                                                "border-radius": "8px",
                                                "text-decoration": "none",
                                                "font-weight": "600",
                                                "display": "inline-block",
                                                "transition": "all 0.3s ease",
                                                "box-shadow": "0 2px 4px rgba(0,0,0,0.1)"
                                            }
                                        )
                                    ]
                                )
                            ]
                        )
                    ]
                ),
            ],
        ),
        footer,
    ],
)
