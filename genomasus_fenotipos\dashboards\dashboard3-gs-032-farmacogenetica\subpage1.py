import pandas as pd
from dash import html, dcc, Input, Output
from components.navbar import create_navbar_for_route
from components.dynamic_sidebar import create_sidebar_for_route
from components.footer import footer
import json
import os
from . import DATA_FILE

def get_current_palette():
    # Paletas de cores definidas
    COLOR_PALETTES = {
        "default": ["#6200EA", "#BB86FC", "#03DAC6", "#018786", "#FFEB3B", "#FFC107", "#FF9800", "#F44336", "#E91E63", "#9C27B0", "#673AB7", "#3F51B5"],
        "colorblind": ["#000000", "#E69F00", "#56B4E9", "#009E73", "#F0E442", "#0072B2", "#D55E00", "#CC79A7", "#999999", "#332288", "#44AA99", "#882255"],
        "autistic": ["#7F95D1", "#7FC9D1", "#7FD195", "#D1C97F", "#D17F95", "#957FD1", "#D1957F", "#95D17F", "#7F95D1", "#7FBCD1", "#D17F95", "#957FD1"],
        "monochrome": ["#0D47A1", "#1565C0", "#1976D2", "#1E88E5", "#2196F3", "#42A5F5", "#64B5F6", "#90CAF9", "#BBDEFB", "#E3F2FD", "#2962FF", "#0D47A1"],
        "pastel": ["#FFB6C1", "#FFD700", "#98FB98", "#ADD8E6", "#FFA07A", "#DDA0DD", "#87CEFA", "#F0E68C", "#E6E6FA", "#FFDAB9", "#B0E0E6", "#FFDEAD"]
    }

    config_file = 'config/app_config.json'
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        palette_key = config.get('color_palette', 'default')
    else:
        palette_key = 'default'

    return COLOR_PALETTES.get(palette_key, COLOR_PALETTES['default'])

# Carregamento e processamento dos dados
df = pd.read_csv(DATA_FILE)

# Categorizar idade
def categorize_age(age):
    if pd.isna(age):
        return 'Não informado'
    elif age < 30:
        return '< 30 anos'
    elif age < 40:
        return '30-39 anos'
    elif age < 50:
        return '40-49 anos'
    elif age < 60:
        return '50-59 anos'
    elif age < 70:
        return '60-69 anos'
    elif age < 80:
        return '70-79 anos'
    else:
        return '≥ 80 anos'

df['idade_categoria'] = df['age_years'].apply(categorize_age)

def create_graph(title, figure, palette_colors=None):
    if palette_colors is None:
        palette_colors = get_current_palette()

    if 'data' in figure and figure['data']:
        for i, trace in enumerate(figure['data']):
            if 'marker' in trace:
                if 'color' in trace['marker'] and isinstance(trace['marker']['color'], str):
                    color_index = i % len(palette_colors)
                    trace['marker']['color'] = palette_colors[color_index]
                elif 'colors' in trace['marker'] and isinstance(trace['marker']['colors'], list):
                    num_colors = len(trace['marker']['colors'])
                    trace['marker']['colors'] = palette_colors[:num_colors]

    return html.Div([
        html.H3(title, style={'textAlign': 'center'}),
        dcc.Graph(figure=figure)
    ], className='graph-container')

# Layout da subpágina de Hipertensão
content = html.Div([
    html.H1("Estatísticas Hipertensão - Farmacogenética (GS-032)", style={'textAlign': 'center'}),



    # Filtros globais
    html.Div([
        html.Label("Filtrar por Sexo:", style={'marginRight': '10px'}),
        dcc.Dropdown(
            id='sexo-filter-subpage1-dashboard3',
            options=[{'label': 'Todos', 'value': 'all'}] +
                    [{'label': 'Feminino', 'value': 1}, {'label': 'Masculino', 'value': 0}],
            value='all',
            clearable=False,
            style={'width': '200px'}
        ),
        html.Label("Filtrar por Raça/Cor:", style={'marginLeft': '20px', 'marginRight': '10px'}),
        dcc.Dropdown(
            id='raca-filter-subpage1-dashboard3',
            options=[{'label': 'Todos', 'value': 'all'}] +
                    [{'label': 'Branco', 'value': 'ethnicity_race_white'},
                     {'label': 'Pardo', 'value': 'ethnicity_race_pardo'},
                     {'label': 'Preto', 'value': 'ethnicity_race_black'},
                     {'label': 'Amarelo', 'value': 'ethnicity_race_yellow'},
                     {'label': 'Indígena', 'value': 'ethnicity_race_indigenous'}],
            value='all',
            clearable=False,
            style={'width': '200px'}
        ),
        html.Label("Filtrar por Faixa Etária:", style={'marginLeft': '20px', 'marginRight': '10px'}),
        dcc.Dropdown(
            id='idade-filter-subpage1-dashboard3',
            options=[{'label': 'Todas', 'value': 'all'}] +
                    [{'label': '< 30 anos', 'value': '< 30 anos'},
                     {'label': '30-39 anos', 'value': '30-39 anos'},
                     {'label': '40-49 anos', 'value': '40-49 anos'},
                     {'label': '50-59 anos', 'value': '50-59 anos'},
                     {'label': '60-69 anos', 'value': '60-69 anos'},
                     {'label': '70-79 anos', 'value': '70-79 anos'},
                     {'label': '≥ 80 anos', 'value': '≥ 80 anos'}],
            value='all',
            clearable=False,
            style={'width': '200px'}
        )
    ], style={'display': 'flex', 'justifyContent': 'center', 'marginBottom': '20px', 'flexWrap': 'wrap'}),

    # Gráficos base (serão atualizados dinamicamente)
    html.Div(id='dynamic-graphs-subpage1-dashboard3')
], className='content')

# Layout principal com a estrutura comum
layout = html.Div(
    className='page-container',
    children=[
        create_navbar_for_route("/dashboard3/farmacogenetica-hipertensao"),
        html.Div([
            create_sidebar_for_route("/dashboard3/farmacogenetica-hipertensao"),
            content,
        ], className='main-section', style={'display': 'flex'}),
        footer,
    ],
)

# Callbacks
def register_callbacks(app):
    @app.callback(
        Output('dynamic-graphs-subpage1-dashboard3', 'children'),
        [Input('sexo-filter-subpage1-dashboard3', 'value'),
         Input('raca-filter-subpage1-dashboard3', 'value'),
         Input('idade-filter-subpage1-dashboard3', 'value')]
    )
    def update_graphs(sexo_filter, raca_filter, idade_filter):
        palette_colors = get_current_palette()

        filtered_df = df.copy()
        if sexo_filter != 'all':
            filtered_df = filtered_df[filtered_df['sex_feminino'] == sexo_filter]

        # Aplicar filtro de raça/cor
        if raca_filter != 'all':
            filtered_df = filtered_df[filtered_df[raca_filter] == 1]

        # Aplicar filtro de idade
        if idade_filter != 'all':
            filtered_df = filtered_df[filtered_df['idade_categoria'] == idade_filter]

        # Pergunta 13: Prevalência da hipertensão no estudo
        hyp_counts = filtered_df['has_hypertension'].value_counts()
        # Ordenar corretamente: False (Não Hipertenso) primeiro, True (Hipertenso) depois
        hyp_labels = ['Hipertenso', 'Não Hipertenso']
        hyp_values = [hyp_counts.get(True, 0), hyp_counts.get(False, 0)]

        graph_1 = create_graph("Prevalência da Hipertensão", {
            'data': [{
                'labels': hyp_labels,
                'values': hyp_values,
                'type': 'pie',
                'marker': {'colors': [palette_colors[0], palette_colors[1]]}
            }],
            'layout': {'title': 'Prevalência da Hipertensão'}
        }, palette_colors)

        # Pergunta 14: Prevalência da hipertensão por sexo (sem idade)
        sexo_hyp_data = []
        sexos = ['Masculino', 'Feminino']
        sexo_values = [0, 1]

        for i, (sexo, value) in enumerate(zip(sexos, sexo_values)):
            subset = filtered_df[filtered_df['sex_feminino'] == value]
            total = len(subset)
            hipertensos = subset['has_hypertension'].sum()
            prevalencia = (hipertensos / total * 100) if total > 0 else 0

            sexo_hyp_data.append({
                'x': [sexo],
                'y': [prevalencia],
                'type': 'bar',
                'name': sexo,
                'marker': {'color': palette_colors[i]},
                'text': [f'{hipertensos}/{total}'],
                'textposition': 'auto'
            })

        graph_2 = create_graph("Prevalência da Hipertensão por Sexo", {
            'data': sexo_hyp_data,
            'layout': {
                'title': 'Prevalência da Hipertensão por Sexo',
                'xaxis': {'title': 'Sexo'},
                'yaxis': {'title': 'Prevalência (%)', 'tickformat': '.1f', 'ticksuffix': '%'},
                'showlegend': False
            }
        }, palette_colors)

        # Pergunta 14: Prevalência da hipertensão pela distribuição etária e sexo
        idade_hyp_data = []
        idade_order = ['< 30 anos', '30-39 anos', '40-49 anos', '50-59 anos', '60-69 anos', '70-79 anos', '≥ 80 anos']

        for i, categoria in enumerate(idade_order):
            if categoria in filtered_df['idade_categoria'].values:
                subset = filtered_df[filtered_df['idade_categoria'] == categoria]
                total = len(subset)
                prevalencia = (subset['has_hypertension'].sum() / total * 100) if total > 0 else 0

                if total > 0:
                    idade_hyp_data.append({
                        'x': [categoria],
                        'y': [prevalencia],
                        'type': 'bar',
                        'marker': {'color': palette_colors[i % len(palette_colors)]},
                        'name': categoria,
                        'showlegend': False
                    })

        graph_idade_hyp = create_graph("Prevalência da Hipertensão por Faixa Etária", {
            'data': idade_hyp_data,
            'layout': {
                'title': 'Prevalência da Hipertensão por Faixa Etária',
                'xaxis': {'title': 'Faixa Etária'},
                'yaxis': {'title': 'Prevalência (%)', 'tickformat': '.1f', 'ticksuffix': '%'},
                'showlegend': False
            }
        }, palette_colors)

        # Pergunta 16: Distribuição de idades em que houve diagnóstico de hipertensão
        idade_diag_data = []
        hipertensos = filtered_df[filtered_df['has_hypertension'] == True]

        if len(hipertensos) > 0:
            idade_diag_data = [{
                'x': hipertensos['age_years'].dropna(),
                'type': 'histogram',
                'marker': {'color': palette_colors[0]},
                'nbinsx': 15
            }]

        graph_idade_diag = create_graph("Distribuição de Idades no Diagnóstico de Hipertensão", {
            'data': idade_diag_data,
            'layout': {
                'title': 'Idades no Diagnóstico de Hipertensão',
                'xaxis': {'title': 'Idade (anos)'},
                'yaxis': {'title': 'Número de casos'}
            }
        }, palette_colors)

        # Heatmap: Hipertensão por idade e sexo
        idade_sexo_hyp_data = []
        x_labels = idade_order
        y_labels = ['Masculino', 'Feminino']
        z_values = []

        for sexo_val, sexo_label in zip([0, 1], y_labels):
            row = []
            for categoria in idade_order:
                subset = filtered_df[(filtered_df['sex_feminino'] == sexo_val) & (filtered_df['idade_categoria'] == categoria)]
                total = len(subset)
                prevalencia = (subset['has_hypertension'].sum() / total * 100) if total > 0 else 0
                row.append(prevalencia)
            z_values.append(row)

        if z_values:
            idade_sexo_hyp_data = [{
                'z': z_values,
                'x': x_labels,
                'y': y_labels,
                'type': 'heatmap',
                'colorscale': 'Blues',
                'showscale': True,
                'colorbar': {'title': 'Prevalência (%)'},
                'hovertemplate': 'Sexo: %{y}<br>Faixa Etária: %{x}<br>Prevalência: %{z:.1f}%<extra></extra>'
            }]

        graph_idade_sexo_hyp = create_graph("Prevalência de Hipertensão por Idade e Sexo", {
            'data': idade_sexo_hyp_data,
            'layout': {
                'title': 'Heatmap: Prevalência de Hipertensão por Idade e Sexo',
                'xaxis': {'title': 'Faixa Etária'},
                'yaxis': {'title': 'Sexo'},
                'height': 400
            }
        }, palette_colors)

        # Novo gráfico de barras: Prevalência da Hipertensão por Idade e Sexo (estilo Renomica-DAC)
        graph_idade_sexo_barras = create_graph("Prevalência da Hipertensão por Idade e Sexo", {
            'data': [
                {
                    'x': filtered_df[(filtered_df['sex_feminino'] == 0) & (filtered_df['has_hypertension'] == True)]['age_years'],
                    'name': 'Masculino Hipertenso',
                    'type': 'histogram',
                    'marker': {'color': '#4472C4'},  # Azul sólido
                    'nbinsx': 15
                },
                {
                    'x': filtered_df[(filtered_df['sex_feminino'] == 1) & (filtered_df['has_hypertension'] == True)]['age_years'],
                    'name': 'Feminino Hipertenso',
                    'type': 'histogram',
                    'marker': {'color': '#E15759'},  # Vermelho sólido
                    'nbinsx': 15
                }
            ],
            'layout': {
                'barmode': 'group',
                'title': 'Prevalência da Hipertensão por Idade e Sexo',
                'xaxis': {'title': 'Idade (anos)'},
                'yaxis': {'title': 'Número de hipertensos'},
                'legend': {'orientation': 'h', 'y': -0.15},
                'bargap': 0.1
            }
        }, palette_colors)

        # Pergunta 15: Prevalência da hipertensão por raça/cor
        raca_columns = {
            'ethnicity_race_white': 'Branco',
            'ethnicity_race_pardo': 'Pardo',
            'ethnicity_race_black': 'Preto',
            'ethnicity_race_yellow': 'Amarelo',
            'ethnicity_race_indigenous': 'Indígena'
        }

        raca_hyp_data = []
        for i, (col, label) in enumerate(raca_columns.items()):
            if col in filtered_df.columns:
                subset = filtered_df[filtered_df[col] == 1]
                total = len(subset)
                if total > 0:
                    hipertensos = subset['has_hypertension'].sum()
                    prevalencia = (hipertensos / total * 100)

                    raca_hyp_data.append({
                        'x': [label],
                        'y': [prevalencia],
                        'type': 'bar',
                        'name': label,
                        'marker': {'color': palette_colors[i % len(palette_colors)]},
                        'text': [f'{hipertensos}/{total}'],
                        'textposition': 'auto'
                    })

        graph_3 = create_graph("Prevalência da Hipertensão por Raça/Cor", {
            'data': raca_hyp_data,
            'layout': {
                'title': 'Prevalência da Hipertensão por Raça/Cor',
                'xaxis': {'title': 'Raça/Cor'},
                'yaxis': {'title': 'Prevalência (%)', 'tickformat': '.1f', 'ticksuffix': '%'},
                'showlegend': False
            }
        }, palette_colors)

        # Pergunta 17: Pessoas com hipertensão que tomam medicamento
        hyp_med_data = []

        # Filtrar apenas pessoas com hipertensão
        hyp_subset = filtered_df[filtered_df['has_hypertension'] == True]

        if len(hyp_subset) > 0:
            # Contar quem usa medicação regular
            usa_med = hyp_subset['uses_regular_medication'].sum()
            nao_usa_med = len(hyp_subset) - usa_med

            hyp_med_data = [{
                'labels': ['Usa medicação regular', 'Não usa medicação regular'],
                'values': [usa_med, nao_usa_med],
                'type': 'pie',
                'marker': {'colors': [palette_colors[0], palette_colors[1]]}
            }]

        graph_4 = create_graph("Pessoas com Hipertensão que Tomam Medicamento", {
            'data': hyp_med_data,
            'layout': {'title': 'Uso de Medicação entre Hipertensos'}
        }, palette_colors)

        # Análise de medicamentos específicos para hipertensão
        # Analisar os medicamentos mais comuns entre hipertensos
        hyp_medications = []
        if len(hyp_subset) > 0 and 'medication_details' in hyp_subset.columns:
            # Medicamentos comuns para hipertensão
            hyp_drugs = ['LOSARTANA', 'ENALAPRIL', 'CAPTOPRIL', 'ANLODIPINO', 'ATENOLOL', 'METOPROLOL', 'CARVEDILOL', 'VALSARTANA']

            med_counts = {}
            for drug in hyp_drugs:
                count = hyp_subset['medication_details'].str.upper().str.contains(drug, na=False).sum()
                if count > 0:
                    med_counts[drug] = count

            if med_counts:
                # Ordenar medicamentos por frequência (decrescente)
                sorted_meds = sorted(med_counts.items(), key=lambda x: x[1], reverse=True)

                hyp_medications = [{
                    'x': [med[0] for med in sorted_meds],
                    'y': [med[1] for med in sorted_meds],
                    'type': 'bar',
                    'marker': {'color': palette_colors[2]}
                }]

        graph_5 = create_graph("Medicamentos Mais Usados por Hipertensos", {
            'data': hyp_medications,
            'layout': {
                'title': 'Medicamentos Anti-hipertensivos Mais Comuns',
                'xaxis': {'title': 'Medicamento'},
                'yaxis': {'title': 'Número de pacientes'},
                'xaxis_tickangle': -45
            }
        }, palette_colors)

        # Heatmap: Hipertensão por sexo e raça/cor
        heatmap_data = []
        x_labels = []
        y_labels = ['Masculino', 'Feminino']
        z_values = []

        # Preparar dados para o heatmap
        for sexo_val, sexo_label in zip([0, 1], y_labels):
            row = []
            for col, raca_label in raca_columns.items():
                if col in filtered_df.columns:
                    subset = filtered_df[(filtered_df['sex_feminino'] == sexo_val) & (filtered_df[col] == 1)]
                    total = len(subset)
                    prevalencia = (subset['has_hypertension'].sum() / total * 100) if total > 0 else 0
                    row.append(prevalencia)
                    if sexo_val == 0:  # Adicionar labels apenas uma vez
                        x_labels.append(raca_label)
            z_values.append(row)

        if x_labels and z_values:
            heatmap_data = [{
                'z': z_values,
                'x': x_labels,
                'y': y_labels,
                'type': 'heatmap',
                'colorscale': 'Reds',
                'showscale': True,
                'colorbar': {'title': 'Prevalência (%)'},
                'hovertemplate': 'Sexo: %{y}<br>Raça/Cor: %{x}<br>Prevalência: %{z:.1f}%<extra></extra>'
            }]

        graph_6 = create_graph("Prevalência de Hipertensão por Sexo e Raça/Cor", {
            'data': heatmap_data,
            'layout': {
                'title': 'Heatmap: Prevalência de Hipertensão por Sexo e Raça/Cor',
                'xaxis': {'title': 'Raça/Cor'},
                'yaxis': {'title': 'Sexo'},
                'height': 400
            }
        }, palette_colors)

        return html.Div([
            html.Div([graph_1, graph_2], className='row'),
            html.Div([graph_idade_hyp, graph_idade_diag], className='row'),
            html.Div([graph_idade_sexo_barras], className='row'),
            html.Div([graph_idade_sexo_hyp, graph_3], className='row'),
            html.Div([graph_4, graph_5], className='row'),
            html.Div([graph_6], className='row')
        ])