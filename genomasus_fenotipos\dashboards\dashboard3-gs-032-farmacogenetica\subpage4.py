import pandas as pd
from dash import html, dcc, Input, Output
from components.navbar import create_navbar_for_route
from components.dynamic_sidebar import create_sidebar_for_route
from components.footer import footer
import json
import os
from . import DATA_FILE

def get_current_palette():
    # Paletas de cores definidas
    COLOR_PALETTES = {
        "default": ["#6200EA", "#BB86FC", "#03DAC6", "#018786", "#FFEB3B", "#FFC107", "#FF9800", "#F44336", "#E91E63", "#9C27B0", "#673AB7", "#3F51B5"],
        "colorblind": ["#000000", "#E69F00", "#56B4E9", "#009E73", "#F0E442", "#0072B2", "#D55E00", "#CC79A7", "#999999", "#332288", "#44AA99", "#882255"],
        "autistic": ["#7F95D1", "#7FC9D1", "#7FD195", "#D1C97F", "#D17F95", "#957FD1", "#D1957F", "#95D17F", "#7F95D1", "#7FBCD1", "#D17F95", "#957FD1"],
        "monochrome": ["#0D47A1", "#1565C0", "#1976D2", "#1E88E5", "#2196F3", "#42A5F5", "#64B5F6", "#90CAF9", "#BBDEFB", "#E3F2FD", "#2962FF", "#0D47A1"],
        "pastel": ["#FFB6C1", "#FFD700", "#98FB98", "#ADD8E6", "#FFA07A", "#DDA0DD", "#87CEFA", "#F0E68C", "#E6E6FA", "#FFDAB9", "#B0E0E6", "#FFDEAD"]
    }
    
    config_file = 'config/app_config.json'
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        palette_key = config.get('color_palette', 'default')
    else:
        palette_key = 'default'
    
    return COLOR_PALETTES.get(palette_key, COLOR_PALETTES['default'])

# Carregamento e processamento dos dados
df = pd.read_csv(DATA_FILE)

def create_graph(title, figure, palette_colors=None):
    if palette_colors is None:
        palette_colors = get_current_palette()
    
    if 'data' in figure and figure['data']:
        for i, trace in enumerate(figure['data']):
            if 'marker' in trace:
                if 'color' in trace['marker'] and isinstance(trace['marker']['color'], str):
                    color_index = i % len(palette_colors)
                    trace['marker']['color'] = palette_colors[color_index]
                elif 'colors' in trace['marker'] and isinstance(trace['marker']['colors'], list):
                    num_colors = len(trace['marker']['colors'])
                    trace['marker']['colors'] = palette_colors[:num_colors]
    
    return html.Div([
        html.H3(title, style={'textAlign': 'center'}),
        dcc.Graph(figure=figure)
    ], className='graph-container')

# Layout da subpágina de Clopidogrel e Doenças Cardíacas
content = html.Div([
    html.H1("Clopidogrel e Doenças Cardíacas - Farmacogenética (GS-032)", style={'textAlign': 'center'}),
    

    
    # Filtros globais
    html.Div([
        html.Label("Filtrar por Sexo:", style={'marginRight': '10px'}),
        dcc.Dropdown(
            id='sexo-filter-subpage4-dashboard3',
            options=[{'label': 'Todos', 'value': 'all'}] + 
                    [{'label': 'Feminino', 'value': 1}, {'label': 'Masculino', 'value': 0}],
            value='all',
            clearable=False,
            style={'width': '200px'}
        ),
        html.Label("Filtrar por Raça/Cor:", style={'marginLeft': '20px', 'marginRight': '10px'}),
        dcc.Dropdown(
            id='raca-filter-subpage4-dashboard3',
            options=[{'label': 'Todos', 'value': 'all'}] +
                    [{'label': 'Branco', 'value': 'ethnicity_race_white'},
                     {'label': 'Pardo', 'value': 'ethnicity_race_pardo'},
                     {'label': 'Preto', 'value': 'ethnicity_race_black'},
                     {'label': 'Amarelo', 'value': 'ethnicity_race_yellow'},
                     {'label': 'Indígena', 'value': 'ethnicity_race_indigenous'}],
            value='all',
            clearable=False,
            style={'width': '200px'}
        )
    ], style={'display': 'flex', 'justifyContent': 'center', 'marginBottom': '20px'}),
    
    # Gráficos base (serão atualizados dinamicamente)
    html.Div(id='dynamic-graphs-subpage4-dashboard3')
], className='content')

# Layout principal com a estrutura comum
layout = html.Div(
    className='page-container',
    children=[
        create_navbar_for_route("/dashboard3/farmacogenetica-clopidogrel-doencas-cardiacas"),
        html.Div([
            create_sidebar_for_route("/dashboard3/farmacogenetica-clopidogrel-doencas-cardiacas"),
            content,  
        ], className='main-section', style={'display': 'flex'}),
        footer,  
    ],
)

# Callbacks
def register_callbacks(app):
    @app.callback(
        Output('dynamic-graphs-subpage4-dashboard3', 'children'),
        [Input('sexo-filter-subpage4-dashboard3', 'value'), Input('raca-filter-subpage4-dashboard3', 'value')]
    )
    def update_graphs(sexo_filter, raca_filter):
        palette_colors = get_current_palette()
        
        filtered_df = df.copy()
        if sexo_filter != 'all':
            filtered_df = filtered_df[filtered_df['sex_feminino'] == sexo_filter]
        
        # Aplicar filtro de raça/cor
        if raca_filter != 'all':
            filtered_df = filtered_df[filtered_df[raca_filter] == 1]

        # Pergunta 30: Distribuição do uso de clopidogrel
        # Converter strings para booleanos para cálculos corretos
        filtered_df_clop = filtered_df.copy()
        filtered_df_clop['uses_clopidogrel_bool'] = filtered_df_clop['uses_clopidogrel'].map({
            'TRUE': True,
            'FALSE': False,
            'missing': False
        }).fillna(False)

        clop_counts = filtered_df_clop['uses_clopidogrel_bool'].value_counts()
        clop_labels = ['Não usa clopidogrel', 'Usa clopidogrel']
        clop_values = [clop_counts.get(False, 0), clop_counts.get(True, 0)]
        
        graph_1 = create_graph("Distribuição do Uso de Clopidogrel", {
            'data': [{
                'labels': clop_labels,
                'values': clop_values,
                'type': 'pie',
                'marker': {'colors': [palette_colors[1], palette_colors[0]]}
            }],
            'layout': {'title': 'Uso de Clopidogrel entre os Participantes'}
        }, palette_colors)

        # Uso de clopidogrel por sexo
        sexo_clop_data = []
        sexos = ['Masculino', 'Feminino']
        sexo_values = [0, 1]
        
        for i, (sexo, value) in enumerate(zip(sexos, sexo_values)):
            subset = filtered_df_clop[filtered_df_clop['sex_feminino'] == value]
            total = len(subset)
            usa_clop = subset['uses_clopidogrel_bool'].sum()  # Agora retorna int
            prevalencia = (usa_clop / total * 100) if total > 0 else 0
            
            sexo_clop_data.append({
                'x': [sexo],
                'y': [prevalencia],
                'type': 'bar',
                'name': sexo,
                'marker': {'color': palette_colors[i]},
                'text': [f'{usa_clop}/{total}'],
                'textposition': 'auto'
            })
        
        graph_2 = create_graph("Uso de Clopidogrel por Sexo", {
            'data': sexo_clop_data,
            'layout': {
                'title': 'Prevalência do Uso de Clopidogrel por Sexo',
                'xaxis': {'title': 'Sexo'},
                'yaxis': {'title': 'Prevalência (%)', 'tickformat': '.1f', 'ticksuffix': '%'},
                'showlegend': False
            }
        }, palette_colors)

        # Uso de clopidogrel por raça/cor
        raca_columns = {
            'ethnicity_race_white': 'Branco',
            'ethnicity_race_pardo': 'Pardo',
            'ethnicity_race_black': 'Preto',
            'ethnicity_race_yellow': 'Amarelo',
            'ethnicity_race_indigenous': 'Indígena'
        }

        raca_clop_data = []
        for i, (col, label) in enumerate(raca_columns.items()):
            if col in filtered_df_clop.columns:
                subset = filtered_df_clop[filtered_df_clop[col] == 1]
                total = len(subset)
                if total > 0:
                    usa_clop = subset['uses_clopidogrel_bool'].sum()  # Agora retorna int
                    prevalencia = (usa_clop / total * 100)

                    raca_clop_data.append({
                        'x': [label],
                        'y': [prevalencia],
                        'type': 'bar',
                        'name': label,
                        'marker': {'color': palette_colors[i % len(palette_colors)]},
                        'text': [f'{usa_clop}/{total}'],
                        'textposition': 'auto'
                    })

        graph_3 = create_graph("Uso de Clopidogrel por Raça/Cor", {
            'data': raca_clop_data,
            'layout': {
                'title': 'Prevalência do Uso de Clopidogrel por Raça/Cor',
                'xaxis': {'title': 'Raça/Cor'},
                'yaxis': {'title': 'Prevalência (%)', 'tickformat': '.1f', 'ticksuffix': '%'},
                'showlegend': False,
                'annotations': [
                    {
                        'text': 'Sem registros',
                        'x': 'Indígena',
                        'y': 1,
                        'showarrow': False,
                        'font': {'size': 12, 'color': 'gray'},
                        'xanchor': 'center'
                    }
                ]
            }
        }, palette_colors)

        # Pergunta 33 e 34: Angioplastia eletiva vs emergência
        angio_columns = ['angioplasty_urgency_elective', 'angioplasty_urgency_emergency', 'angioplasty_urgency_missing']
        angio_labels = ['Angioplastia Eletiva', 'Angioplastia de Emergência', 'Não informado']
        angio_values = []

        for col in angio_columns:
            if col in filtered_df.columns:
                count = filtered_df[col].sum()
                angio_values.append(count)
            else:
                angio_values.append(0)

        graph_4 = create_graph("Distribuição dos Tipos de Angioplastia", {
            'data': [{
                'labels': angio_labels,
                'values': angio_values,
                'type': 'pie',
                'marker': {'colors': palette_colors[:len(angio_labels)]}
            }],
            'layout': {'title': 'Tipos de Angioplastia Realizados'}
        }, palette_colors)

        # Pergunta 31: Prevalência de hipertensão entre usuários de clopidogrel
        clop_hyp_data = []

        # Usuários de clopidogrel (usando a coluna booleana)
        clop_users = filtered_df_clop[filtered_df_clop['uses_clopidogrel_bool'] == True]
        clop_non_users = filtered_df_clop[filtered_df_clop['uses_clopidogrel_bool'] == False]

        if len(clop_users) > 0:
            hyp_clop_users = clop_users['has_hypertension'].sum()
            prev_clop_users = (hyp_clop_users / len(clop_users) * 100)
        else:
            prev_clop_users = 0
            hyp_clop_users = 0

        if len(clop_non_users) > 0:
            hyp_non_users = clop_non_users['has_hypertension'].sum()
            prev_non_users = (hyp_non_users / len(clop_non_users) * 100)
        else:
            prev_non_users = 0
            hyp_non_users = 0

        clop_hyp_data = [{
            'x': ['Usa Clopidogrel', 'Não usa Clopidogrel'],
            'y': [prev_clop_users, prev_non_users],
            'type': 'bar',
            'marker': {'color': [palette_colors[0], palette_colors[1]]},
            'text': [f'{hyp_clop_users}/{len(clop_users)}' if len(clop_users) > 0 else '0/0',
                     f'{hyp_non_users}/{len(clop_non_users)}' if len(clop_non_users) > 0 else '0/0'],
            'textposition': 'auto'
        }]

        graph_5 = create_graph("Prevalência de Hipertensão por Uso de Clopidogrel", {
            'data': clop_hyp_data,
            'layout': {
                'title': 'Hipertensão entre Usuários e Não Usuários de Clopidogrel',
                'xaxis': {'title': 'Uso de Clopidogrel'},
                'yaxis': {'title': 'Prevalência de Hipertensão (%)', 'tickformat': '.1f', 'ticksuffix': '%'},
                'showlegend': False
            }
        }, palette_colors)

        # Pergunta 32: Prevalência de diabetes entre usuários de clopidogrel
        clop_diabetes_data = []

        if len(clop_users) > 0:
            # Converter colunas de diabetes para numéricas se necessário
            diabetes_t1_clop = pd.to_numeric(clop_users.get('diabetes_type_type_1', 0), errors='coerce').fillna(0).sum()
            diabetes_t2_clop = pd.to_numeric(clop_users.get('diabetes_type_type_2', 0), errors='coerce').fillna(0).sum()
            diabetes_clop_users = int(diabetes_t1_clop + diabetes_t2_clop)
            prev_diabetes_clop = (diabetes_clop_users / len(clop_users) * 100)
        else:
            prev_diabetes_clop = 0
            diabetes_clop_users = 0

        if len(clop_non_users) > 0:
            # Converter colunas de diabetes para numéricas se necessário
            diabetes_t1_non = pd.to_numeric(clop_non_users.get('diabetes_type_type_1', 0), errors='coerce').fillna(0).sum()
            diabetes_t2_non = pd.to_numeric(clop_non_users.get('diabetes_type_type_2', 0), errors='coerce').fillna(0).sum()
            diabetes_non_users = int(diabetes_t1_non + diabetes_t2_non)
            prev_diabetes_non = (diabetes_non_users / len(clop_non_users) * 100)
        else:
            prev_diabetes_non = 0
            diabetes_non_users = 0

        clop_diabetes_data = [{
            'x': ['Usa Clopidogrel', 'Não usa Clopidogrel'],
            'y': [prev_diabetes_clop, prev_diabetes_non],
            'type': 'bar',
            'marker': {'color': [palette_colors[2], palette_colors[3]]},
            'text': [f'{diabetes_clop_users}/{len(clop_users)}' if len(clop_users) > 0 else '0/0',
                     f'{diabetes_non_users}/{len(clop_non_users)}' if len(clop_non_users) > 0 else '0/0'],
            'textposition': 'auto'
        }]

        graph_6 = create_graph("Prevalência de Diabetes por Uso de Clopidogrel", {
            'data': clop_diabetes_data,
            'layout': {
                'title': 'Diabetes entre Usuários e Não Usuários de Clopidogrel',
                'xaxis': {'title': 'Uso de Clopidogrel'},
                'yaxis': {'title': 'Prevalência de Diabetes (%)', 'tickformat': '.1f', 'ticksuffix': '%'},
                'showlegend': False
            }
        }, palette_colors)

        return html.Div([
            html.Div([graph_1, graph_2], className='row'),
            html.Div([graph_3, graph_4], className='row'),
            html.Div([graph_5, graph_6], className='row')
        ])
