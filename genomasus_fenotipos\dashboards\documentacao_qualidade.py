from dash import html, dcc
from components.back_to_home_button import create_back_to_home_button

# Layout da página "Documentação de Dimensões de Qualidade"
layout = html.Div(
    style={
        'padding': '40px 20px',
        'maxWidth': '1200px',
        'margin': '0 auto',
        'lineHeight': '1.8',
        'color': '#333',
        'backgroundColor': '#f9f9f9',
        'fontFamily': 'Arial, sans-serif',
    },
    children=[


        # Título principal
        html.H1(
            "Dimensões de Qualidade – Visão Geral",
            style={
                'textAlign': 'center',
                'color': '#007BFF',
                'marginBottom': '40px',
                'fontSize': 'clamp(24px, 5vw, 36px)',
                'fontWeight': '600',
            }
        ),

        # Descrição do projeto
        html.Div(
            style={
                'backgroundColor': '#ffffff',
                'padding': '20px',
                'borderRadius': '10px',
                'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                'marginBottom': '40px',
            },
            children=[
                html.P(
                    "As dimensões de qualidade são essenciais para garantir a integridade, confiabilidade e utilidade dos dados, sobretudo em projetos complexos como o Genoma SUS Brasil. Nesse contexto, assegurar que os dados sejam completos, consistentes, conformes e que representem fielmente a realidade é crucial para minimizar vieses, aprimorar a interpretação dos resultados e fundamentar decisões em saúde pública. Essas dimensões não só asseguram a segurança e a ética na utilização dos dados, mas também potencializam o valor científico e o impacto social das análises realizadas.",
                    style={'color': '#333'}
                ),
                html.P(
                    "As métricas de qualidade são os indicadores mensuráveis que avaliam cada uma dessas dimensões, permitindo quantificar e monitorar se os dados atendem aos padrões estabelecidos, além de identificar discrepâncias que possam comprometer análises robustas. As métricas descritas a seguir foram cuidadosamente estudadas, discutidas e selecionadas por nossa equipe, oferecendo uma abordagem robusta e alinhada com os desafios específicos do Genoma SUS Brasil, garantindo segurança, ética e um impacto positivo nos resultados em saúde pública.",
                    style={'color': '#333'}
                ),
            ],
        ),

        # Dimensões Conceituais
        html.Div(
            style={
                'backgroundColor': '#ffffff',
                'padding': '20px',
                'borderRadius': '10px',
                'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                'marginBottom': '40px',
            },
            children=[
                html.H2("Principais Dimensões Conceituais"),
                html.Ul(
                    style={'listStyleType': 'disc', 'paddingLeft': '20px'},
                    children=[
                        html.Li([
                            html.Span(html.Strong("Completude: "), style={'color': '#333'}),
                            html.Span("Garante que todos os dados esperados estejam presentes, sem registros duplicados ou ausentes.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Conformidade: "), style={'color': '#333'}),
                            html.Span("Verifica se os dados obedecem a regras e padrões predefinidos (como formatos e restrições sintáticas), assegurando validade e padronização.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Consistência: "), style={'color': '#333'}),
                            html.Span("Assegura a uniformidade dos dados entre diferentes campos, evitando contradições e discrepâncias internas.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Concordância: "), style={'color': '#333'}),
                            html.Span("Mede a harmonia entre dados registrados em diferentes fontes ou sistemas, promovendo interoperabilidade e eliminando conflitos.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Relevância: "), style={'color': '#333'}),
                            html.Span("Determina se os dados são adequados para responder a questões científicas ou clínicas, conforme o escopo da análise.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Representatividade: "), style={'color': '#333'}),
                            html.Span("Verifica se os dados refletem fielmente a população ou fenômeno estudado, minimizando vieses.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Contextualização: "), style={'color': '#333'}),
                            html.Span("Considera o contexto histórico, geográfico ou clínico da coleta dos dados para evitar interpretações equivocadas.", style={'color': '#333'})
                        ]),
                    ]
                ),
            ],
        ),

        # Outras Métricas
        html.Div(
            style={
                'backgroundColor': '#ffffff',
                'padding': '20px',
                'borderRadius': '10px',
                'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                'marginBottom': '40px',
            },
            children=[
                html.H2("Outras Métricas"),
                html.P(
                    "O projeto também abrange métricas complementares, agrupadas em:",
                    style={'color': '#333'}
                ),
                html.Ul(
                    style={'listStyleType': 'disc', 'paddingLeft': '20px'},
                    children=[
                        html.Li([
                            html.Span(html.Strong("Métricas Aglutinadas: "), style={'color': '#333'}),
                            html.Span("Englobam aspectos como captura, unicidade, comparabilidade e interoperabilidade, que reforçam as dimensões primárias.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Métricas Estruturais (Técnicas): "), style={'color': '#333'}),
                            html.Span("Referem-se à segurança, acessibilidade, atualidade, flexibilidade, imediatismo, integridade relacional e rastreabilidade dos dados.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Métricas Longitudinais: "), style={'color': '#333'}),
                            html.Span("Envolvem a atemporalidade, temporalidade e estabilidade dos dados, essenciais para análises que acompanham sua evolução ao longo do tempo.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Métricas para um Segundo Momento: "), style={'color': '#333'}),
                            html.Span("Abrangem conceitos como artificialidade, inteligibilidade, validade, confiabilidade e plausibilidade, complementando a avaliação global dos dados.", style={'color': '#333'})
                        ]),
                    ]
                ),
            ],
        ),

        # Botão padronizado "Voltar à Página Inicial"
        create_back_to_home_button(),

        # Rodapé
        html.Footer(
            style={
                'marginTop': '60px',
                'padding': '20px',
                'backgroundColor': '#f1f1f1',
                'color': '#333',
                'textAlign': 'center',
            },
            children=[
                html.P("© 2025 Projeto GenomaSUS - Fenótipos. Todos os direitos reservados.", style={'color': '#333'}),
                html.P(
                    "Contato: ",
                    style={'marginTop': '10px', 'color': '#333'}
                ),
                dcc.Link(
                    "<EMAIL>",
                    href="mailto:<EMAIL>",
                    style={'color': '#007BFF', 'textDecoration': 'none'}
                ),
            ]
        ),
    ]
)