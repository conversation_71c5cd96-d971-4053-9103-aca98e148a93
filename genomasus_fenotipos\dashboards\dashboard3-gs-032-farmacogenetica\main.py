import pandas as pd
import numpy as np
from dash import html, dcc, Dash, Input, Output, State
from components.navbar import create_navbar_for_route
from components.dynamic_sidebar import create_sidebar_for_route
from components.footer import footer
import plotly.express as px
import json
import os
from . import DATA_FILE

def get_current_palette():
    # Paletas de cores definidas
    COLOR_PALETTES = {
        "default": ["#6200EA", "#BB86FC", "#03DAC6", "#018786", "#FFEB3B", "#FFC107", "#FF9800", "#F44336", "#E91E63", "#9C27B0", "#673AB7", "#3F51B5"],
        "colorblind": ["#000000", "#E69F00", "#56B4E9", "#009E73", "#F0E442", "#0072B2", "#D55E00", "#CC79A7", "#999999", "#332288", "#44AA99", "#882255"],
        "autistic": ["#7F95D1", "#7FC9D1", "#7FD195", "#D1C97F", "#D17F95", "#957FD1", "#D1957F", "#95D17F", "#7F95D1", "#7FBCD1", "#D17F95", "#957FD1"],
        "monochrome": ["#0D47A1", "#1565C0", "#1976D2", "#1E88E5", "#2196F3", "#42A5F5", "#64B5F6", "#90CAF9", "#BBDEFB", "#E3F2FD", "#2962FF", "#0D47A1"],
        "pastel": ["#FFB6C1", "#FFD700", "#98FB98", "#ADD8E6", "#FFA07A", "#DDA0DD", "#87CEFA", "#F0E68C", "#E6E6FA", "#FFDAB9", "#B0E0E6", "#FFDEAD"]
    }

    config_file = 'config/app_config.json'
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        palette_key = config.get('color_palette', 'default')
    else:
        palette_key = 'default'

    return COLOR_PALETTES.get(palette_key, COLOR_PALETTES['default'])

# Carregamento e processamento dos dados
df = pd.read_csv(DATA_FILE)

# Processar variáveis categóricas
# Sexo (já está como binária)
df['sexo_feminino'] = df['sex_feminino'].astype(int)
df['sexo_masculino'] = 1 - df['sex_feminino'].astype(int)

# Criar variáveis binárias para raça/cor (já existem como colunas separadas)
# ethnicity_race_black, ethnicity_race_white, ethnicity_race_pardo, etc.

# Calcular IMC
df['imc'] = df['weight_kg'] / (df['height_cm'] / 100) ** 2

# Categorizar IMC
def categorize_imc(imc):
    if pd.isna(imc):
        return 'Missing'
    elif imc < 18.5:
        return 'Abaixo do peso'
    elif imc < 25:
        return 'Peso normal'
    elif imc < 30:
        return 'Sobrepeso'
    else:
        return 'Obesidade'

df['imc_categoria'] = df['imc'].apply(categorize_imc)

# Categorizar idade
def categorize_age(age):
    if pd.isna(age):
        return 'Não informado'
    elif age < 30:
        return '< 30 anos'
    elif age < 40:
        return '30-39 anos'
    elif age < 50:
        return '40-49 anos'
    elif age < 60:
        return '50-59 anos'
    elif age < 70:
        return '60-69 anos'
    elif age < 80:
        return '70-79 anos'
    else:
        return '≥ 80 anos'

df['idade_categoria'] = df['age_years'].apply(categorize_age)

def create_graph(title, figure, palette_colors=None):
    if palette_colors is None:
        palette_colors = get_current_palette()

    if 'data' in figure and figure['data']:
        for i, trace in enumerate(figure['data']):
            if 'marker' in trace:
                if 'color' in trace['marker'] and isinstance(trace['marker']['color'], str):
                    color_index = i % len(palette_colors)
                    trace['marker']['color'] = palette_colors[color_index]
                elif 'colors' in trace['marker'] and isinstance(trace['marker']['colors'], list):
                    num_colors = len(trace['marker']['colors'])
                    trace['marker']['colors'] = palette_colors[:num_colors]

    return html.Div([
        html.H3(title, style={'textAlign': 'center'}),
        dcc.Graph(figure=figure)
    ], className='graph-container')

layout = html.Div(
    className='page-container',
    children=[
        html.Link(
            rel='stylesheet',
            href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
        ),

        create_navbar_for_route("/dashboard3/farmacogenetica-sociodemografico"),
        html.Div(
            className='main-wrapper',
            id='main-wrapper',
            children=[
                create_sidebar_for_route("/dashboard3/farmacogenetica-sociodemografico"),
                html.Div(
                    className='content',
                    children=[
                        html.Div(id='dashboard3-content'),
                    ],
                ),
            ],
        ),
        footer,
    ],
)

# Filtros globais
filters = html.Div([
    html.Label("Filtrar por Sexo:", style={'marginRight': '10px'}),
    dcc.Dropdown(
        id='sexo-filter-dashboard3',
        options=[{'label': 'Todos', 'value': 'all'}] +
                [{'label': 'Feminino', 'value': 1}, {'label': 'Masculino', 'value': 0}],
        value='all',
        clearable=False,
        style={'width': '200px'}
    ),
    html.Label("Filtrar por Raça/Cor:", style={'marginLeft': '20px', 'marginRight': '10px'}),
    dcc.Dropdown(
        id='raca-filter-dashboard3',
        options=[{'label': 'Todos', 'value': 'all'}] +
                [{'label': 'Branco', 'value': 'ethnicity_race_white'},
                 {'label': 'Pardo', 'value': 'ethnicity_race_pardo'},
                 {'label': 'Preto', 'value': 'ethnicity_race_black'},
                 {'label': 'Amarelo', 'value': 'ethnicity_race_yellow'},
                 {'label': 'Indígena', 'value': 'ethnicity_race_indigenous'}],
        value='all',
        clearable=False,
        style={'width': '200px'}
    ),
    html.Label("Filtrar por Faixa Etária:", style={'marginLeft': '20px', 'marginRight': '10px'}),
    dcc.Dropdown(
        id='idade-filter-dashboard3',
        options=[{'label': 'Todas', 'value': 'all'}] +
                [{'label': '< 30 anos', 'value': '< 30 anos'},
                 {'label': '30-39 anos', 'value': '30-39 anos'},
                 {'label': '40-49 anos', 'value': '40-49 anos'},
                 {'label': '50-59 anos', 'value': '50-59 anos'},
                 {'label': '60-69 anos', 'value': '60-69 anos'},
                 {'label': '70-79 anos', 'value': '70-79 anos'},
                 {'label': '≥ 80 anos', 'value': '≥ 80 anos'}],
        value='all',
        clearable=False,
        style={'width': '200px'}
    )
], style={'display': 'flex', 'justifyContent': 'center', 'marginBottom': '20px', 'flexWrap': 'wrap'})

graphs = html.Div(id='dynamic-graphs-dashboard3')

dashboard3_main_content = html.Div([
    html.H1("Estatísticas Sociodemográficas - Farmacogenética (GS-032)", style={'textAlign': 'center'}),

    # Aviso sobre limitações dos dados
    html.Div([
        html.Div([
            html.I(className="fa fa-info-circle", style={'marginRight': '10px', 'color': '#d9534f'}),
            html.Strong("Importante: "),
            "Este estudo não possui dados de escolaridade dos participantes, "
            "limitando algumas análises educacionais."
        ], style={
            'backgroundColor': '#f9e6e6',
            'border': '1px solid #d9534f',
            'borderRadius': '5px',
            'padding': '15px',
            'margin': '20px 0',
            'fontSize': '14px',
            'color': '#d9534f'
        })
    ]),

    filters,
    graphs
])

# Callbacks
def register_callbacks(app):
    @app.callback(
        Output('dynamic-graphs-dashboard3', 'children'),
        [Input('sexo-filter-dashboard3', 'value'),
         Input('raca-filter-dashboard3', 'value'),
         Input('idade-filter-dashboard3', 'value')]
    )
    def update_graphs(sexo_filter, raca_filter, idade_filter):
        palette_colors = get_current_palette()

        filtered_df = df.copy()
        if sexo_filter != 'all':
            filtered_df = filtered_df[filtered_df['sex_feminino'] == sexo_filter]

        if raca_filter != 'all':
            filtered_df = filtered_df[filtered_df[raca_filter] == 1]

        if idade_filter != 'all':
            filtered_df = filtered_df[filtered_df['idade_categoria'] == idade_filter]

        # Pergunta 2: Distribuição do sexo dos participantes
        sexo_counts = filtered_df['sex_feminino'].value_counts()
        sexo_labels = ['Masculino', 'Feminino']
        sexo_values = [sexo_counts.get(0, 0), sexo_counts.get(1, 0)]

        graph_1 = create_graph("Distribuição do Sexo dos Participantes", {
            'data': [{
                'labels': sexo_labels,
                'values': sexo_values,
                'type': 'pie',
                'marker': {'colors': palette_colors[:2]}
            }],
            'layout': {'title': 'Distribuição por Sexo'}
        }, palette_colors)

        # Pergunta 1: Distribuição etária dos participantes
        idade_counts = filtered_df['idade_categoria'].value_counts()
        # Ordenar as categorias de idade de forma lógica
        idade_order = ['< 30 anos', '30-39 anos', '40-49 anos', '50-59 anos', '60-69 anos', '70-79 anos', '≥ 80 anos', 'Não informado']
        idade_labels = []
        idade_values = []

        for categoria in idade_order:
            if categoria in idade_counts.index:
                idade_labels.append(categoria)
                idade_values.append(idade_counts[categoria])

        graph_idade = create_graph("Distribuição Etária dos Participantes", {
            'data': [{
                'x': idade_labels,
                'y': idade_values,
                'type': 'bar',
                'marker': {'color': palette_colors[3]}
            }],
            'layout': {
                'title': 'Distribuição por Faixa Etária',
                'xaxis': {'title': 'Faixa Etária'},
                'yaxis': {'title': 'Número de participantes'}
            }
        }, palette_colors)

        # Histograma de idade contínua
        graph_idade_hist = create_graph("Distribuição Contínua da Idade", {
            'data': [{
                'x': filtered_df['age_years'].dropna(),
                'type': 'histogram',
                'marker': {'color': palette_colors[4]},
                'nbinsx': 15
            }],
            'layout': {
                'title': 'Distribuição da Idade (anos)',
                'xaxis': {'title': 'Idade (anos)'},
                'yaxis': {'title': 'Número de participantes'}
            }
        }, palette_colors)

        # Pergunta 3: Distribuição etária por sexo dos participantes
        idade_sexo_data = []
        for i, categoria in enumerate(idade_order):
            if categoria in filtered_df['idade_categoria'].values:
                male_count = len(filtered_df[(filtered_df['idade_categoria'] == categoria) & (filtered_df['sex_feminino'] == 0)])
                female_count = len(filtered_df[(filtered_df['idade_categoria'] == categoria) & (filtered_df['sex_feminino'] == 1)])

                # Adicionar barra masculina se houver dados
                if male_count > 0:
                    idade_sexo_data.append({
                        'x': [categoria],
                        'y': [male_count],
                        'name': f'{categoria} – Masculino',
                        'type': 'bar',
                        'marker': {'color': palette_colors[0]},
                        'showlegend': True
                    })

                # Adicionar barra feminina se houver dados
                if female_count > 0:
                    idade_sexo_data.append({
                        'x': [categoria],
                        'y': [female_count],
                        'name': f'{categoria} – Feminino',
                        'type': 'bar',
                        'marker': {'color': palette_colors[1]},
                        'showlegend': True
                    })

        graph_idade_sexo = create_graph("Distribuição Etária por Sexo", {
            'data': idade_sexo_data,
            'layout': {
                'barmode': 'group',
                'title': 'Distribuição Etária por Sexo',
                'xaxis': {'title': 'Faixa Etária'},
                'yaxis': {'title': 'Número de participantes'},
                'legend': {
                    'orientation': 'v',
                    'x': 1.02,
                    'y': 1,
                    'bgcolor': 'rgba(255,255,255,0.9)',
                    'bordercolor': 'rgba(0,0,0,0)',
                    'borderwidth': 0,
                    'font': {'size': 10}
                },
                'margin': {'r': 180}
            }
        }, palette_colors)

        # Pergunta 4: Distribuição de renda dos participantes
        renda_counts = filtered_df['family_income_category'].value_counts()
        renda_labels = []
        renda_values = []

        # Mapear categorias de renda para nomes mais legíveis
        renda_mapping = {
            'less_than_1_salary': 'Menos de 1 salário',
            'up_to_2_salaries': 'Até 2 salários',
            '2_to_5_salaries': '2 a 5 salários',
            'more_than_5_salaries': 'Mais de 5 salários',
            'missing': 'Não informado'
        }

        for category, count in renda_counts.items():
            renda_labels.append(renda_mapping.get(category, category))
            renda_values.append(count)

        graph_2 = create_graph("Distribuição de Renda dos Participantes", {
            'data': [{
                'labels': renda_labels,
                'values': renda_values,
                'type': 'pie',
                'marker': {'colors': palette_colors[:len(renda_labels)]}
            }],
            'layout': {'title': 'Distribuição de Renda'}
        }, palette_colors)

        # Pergunta 5: Distribuição de renda por sexo
        renda_sexo_data = []
        for i, (category, readable_name) in enumerate(renda_mapping.items()):
            if category in filtered_df['family_income_category'].values:
                male_count = len(filtered_df[(filtered_df['family_income_category'] == category) & (filtered_df['sex_feminino'] == 0)])
                female_count = len(filtered_df[(filtered_df['family_income_category'] == category) & (filtered_df['sex_feminino'] == 1)])

                # Adicionar barra masculina se houver dados
                if male_count > 0:
                    renda_sexo_data.append({
                        'x': [readable_name],
                        'y': [male_count],
                        'name': f'{readable_name} – Masculino',
                        'type': 'bar',
                        'marker': {'color': palette_colors[0]},
                        'showlegend': True
                    })

                # Adicionar barra feminina se houver dados
                if female_count > 0:
                    renda_sexo_data.append({
                        'x': [readable_name],
                        'y': [female_count],
                        'name': f'{readable_name} – Feminino',
                        'type': 'bar',
                        'marker': {'color': palette_colors[1]},
                        'showlegend': True
                    })

        graph_3 = create_graph("Distribuição de Renda por Sexo", {
            'data': renda_sexo_data,
            'layout': {
                'barmode': 'group',
                'title': 'Distribuição de Renda por Sexo',
                'xaxis': {'title': 'Categoria de Renda'},
                'yaxis': {'title': 'Número de participantes'},
                'legend': {
                    'orientation': 'v',
                    'x': 1.02,
                    'y': 1,
                    'bgcolor': 'rgba(255,255,255,0.9)',
                    'bordercolor': 'rgba(0,0,0,0)',
                    'borderwidth': 0,
                    'font': {'size': 10}
                },
                'margin': {'r': 180}
            }
        }, palette_colors)

        # Pergunta 7: Distribuição de raça/cor dos participantes
        raca_data = []
        raca_labels = []
        raca_counts = []

        raca_columns = {
            'ethnicity_race_white': 'Branco',
            'ethnicity_race_pardo': 'Pardo',
            'ethnicity_race_black': 'Preto',
            'ethnicity_race_yellow': 'Amarelo',
            'ethnicity_race_indigenous': 'Indígena',
            'ethnicity_race_missing': 'Não informado'
        }

        for col, label in raca_columns.items():
            if col in filtered_df.columns:
                count = filtered_df[col].sum()
                if count > 0:
                    raca_labels.append(label)
                    raca_counts.append(count)

        graph_4 = create_graph("Distribuição de Raça/Cor dos Participantes", {
            'data': [{
                'labels': raca_labels,
                'values': raca_counts,
                'type': 'pie',
                'marker': {'colors': palette_colors[:len(raca_labels)]}
            }],
            'layout': {'title': 'Distribuição de Raça/Cor'}
        }, palette_colors)

        # Pergunta 8: Distribuição de raça/cor por sexo
        raca_sexo_data = []
        for i, (col, label) in enumerate(raca_columns.items()):
            if col in filtered_df.columns and filtered_df[col].sum() > 0:
                male_count = len(filtered_df[(filtered_df[col] == 1) & (filtered_df['sex_feminino'] == 0)])
                female_count = len(filtered_df[(filtered_df[col] == 1) & (filtered_df['sex_feminino'] == 1)])

                # Adicionar barra masculina se houver dados
                if male_count > 0:
                    raca_sexo_data.append({
                        'x': [label],
                        'y': [male_count],
                        'name': f'{label} – Masculino',
                        'type': 'bar',
                        'marker': {'color': palette_colors[0]},
                        'showlegend': True
                    })

                # Adicionar barra feminina se houver dados
                if female_count > 0:
                    raca_sexo_data.append({
                        'x': [label],
                        'y': [female_count],
                        'name': f'{label} – Feminino',
                        'type': 'bar',
                        'marker': {'color': palette_colors[1]},
                        'showlegend': True
                    })

        graph_5 = create_graph("Distribuição de Raça/Cor por Sexo", {
            'data': raca_sexo_data,
            'layout': {
                'barmode': 'group',
                'title': 'Distribuição de Raça/Cor por Sexo',
                'xaxis': {'title': 'Raça/Cor'},
                'yaxis': {'title': 'Número de participantes'},
                'legend': {
                    'orientation': 'v',
                    'x': 1.02,
                    'y': 1,
                    'bgcolor': 'rgba(255,255,255,0.9)',
                    'bordercolor': 'rgba(0,0,0,0)',
                    'borderwidth': 0,
                    'font': {'size': 10}
                },
                'margin': {'r': 180}
            }
        }, palette_colors)

        # Pergunta 23: Distribuição do IMC dos participantes
        graph_6 = create_graph("Distribuição do IMC dos Participantes", {
            'data': [{'x': filtered_df['imc'].dropna(), 'type': 'histogram', 'marker': {'color': palette_colors[2]}, 'nbinsx': 20}],
            'layout': {
                'title': 'Distribuição do IMC',
                'xaxis': {'title': 'IMC (kg/m²)'},
                'yaxis': {'title': 'Número de participantes'}
            }
        }, palette_colors)

        # Categorias de IMC
        imc_counts = filtered_df['imc_categoria'].value_counts()
        graph_7 = create_graph("Distribuição por Categorias de IMC", {
            'data': [{
                'labels': imc_counts.index.tolist(),
                'values': imc_counts.values.tolist(),
                'type': 'pie',
                'marker': {'colors': palette_colors[:len(imc_counts)]}
            }],
            'layout': {'title': 'Categorias de IMC'}
        }, palette_colors)

        # Distribuição de renda por raça/cor (Pergunta 6)
        renda_raca_matrix = []
        renda_categories = list(renda_mapping.keys())
        raca_cols = list(raca_columns.keys())

        # Criar matriz para heatmap
        z_values = []
        x_labels = [raca_columns[col] for col in raca_cols if col in filtered_df.columns]
        y_labels = [renda_mapping[cat] for cat in renda_categories]

        for renda_cat in renda_categories:
            row = []
            for raca_col in raca_cols:
                if raca_col in filtered_df.columns:
                    count = len(filtered_df[(filtered_df['family_income_category'] == renda_cat) & (filtered_df[raca_col] == 1)])
                    row.append(count)
            z_values.append(row)

        graph_8 = create_graph("Distribuição de Renda por Raça/Cor", {
            'data': [{
                'z': z_values,
                'x': x_labels,
                'y': y_labels,
                'type': 'heatmap',
                'colorscale': 'Blues',
                'showscale': True,
                'colorbar': {'title': 'Número de participantes'},
                'hovertemplate': 'Raça/Cor: %{x}<br>Renda: %{y}<br>Número: %{z}<extra></extra>'
            }],
            'layout': {
                'title': 'Distribuição de Renda por Raça/Cor',
                'xaxis': {'title': 'Raça/Cor'},
                'yaxis': {'title': 'Categoria de Renda'},
                'height': 500
            }
        }, palette_colors)

        return html.Div([
            html.Div([graph_1, graph_idade], className='row'),
            html.Div([graph_idade_hist, graph_idade_sexo], className='row'),
            html.Div([graph_2, graph_3], className='row'),
            html.Div([graph_4, graph_5], className='row'),
            html.Div([graph_6, graph_7], className='row'),
            html.Div([graph_8], className='row')
        ])

    @app.callback(
        Output('dashboard3-content', 'children'),
        [Input('url', 'pathname')]
    )
    def display_dashboard3_content(pathname):
        if pathname in ['/dashboard3', '/dashboard3/farmacogenetica-sociodemografico']:
            return dashboard3_main_content
        else:
            return dashboard3_main_content