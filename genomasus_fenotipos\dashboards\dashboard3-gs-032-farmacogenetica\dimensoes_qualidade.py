#!/usr/bin/env python3
"""
Página de Dimensões de Qualidade do Estudo GS-032 Farmacogenética
Contém documentação sobre qualidade e integridade dos dados
"""

from dash import html, dcc
from components.navbar import create_navbar_for_route
from components.dynamic_sidebar import create_sidebar_for_route
from components.footer import footer

# Conteúdo da página de dimensões de qualidade
content = html.Div(
    className='content',
    style={
        'padding': '40px 20px',
        'maxWidth': '1200px',
        'margin': '0 auto',
        'lineHeight': '1.8',
        'color': '#333',
        'backgroundColor': '#f9f9f9',
        'fontFamily': 'Arial, sans-serif',
    },
    children=[
        # Título principal
        html.H1(
            "Dimensões de Qualidade – Estudo GS-032 Farmacogenética",
            style={
                'textAlign': 'center',
                'color': '#6200EA',
                'marginBottom': '40px',
                'fontSize': 'clamp(24px, 5vw, 36px)',
                'fontWeight': '600',
            }
        ),

        # Descrição específica do estudo
        html.Div(
            style={
                'backgroundColor': '#ffffff',
                'padding': '20px',
                'borderRadius': '10px',
                'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                'marginBottom': '40px',
            },
            children=[
                html.P(
                    "O estudo GS-032 EPIGEN-II-PGx-cardiovascular representa um marco na pesquisa farmacogenética brasileira, focando especificamente em doenças cardiovasculares e resposta a medicamentos como o clopidogrel. A qualidade dos dados neste estudo é fundamental para garantir a validade das análises farmacogenéticas e a aplicabilidade clínica dos resultados. Com 192 participantes e 134 variáveis analisadas, o controle de qualidade abrange desde a coleta de dados clínicos até a análise de biomarcadores genéticos.",
                    style={'color': '#333'}
                ),
                html.P(
                    "As dimensões de qualidade aplicadas a este estudo consideram as especificidades da pesquisa farmacogenética, incluindo a precisão dos dados genéticos, a consistência das informações clínicas, e a representatividade da amostra em relação à diversidade étnico-racial brasileira. Essas métricas são essenciais para assegurar que os resultados possam contribuir efetivamente para a medicina personalizada no contexto do Sistema Único de Saúde (SUS).",
                    style={'color': '#333'}
                ),
            ],
        ),

        # Características específicas do estudo
        html.Div(
            style={
                'backgroundColor': '#ffffff',
                'padding': '20px',
                'borderRadius': '10px',
                'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                'marginBottom': '40px',
            },
            children=[
                html.H2("Características de Qualidade do Estudo GS-032", style={'color': '#6200EA'}),
                html.Ul(
                    style={'listStyleType': 'disc', 'paddingLeft': '20px'},
                    children=[
                        html.Li([
                            html.Span(html.Strong("Completude dos Dados Cardiovasculares: "), style={'color': '#333'}),
                            html.Span("192 participantes com dados completos de procedimentos cardiovasculares, incluindo angioplastia eletiva e de emergência.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Consistência Farmacológica: "), style={'color': '#333'}),
                            html.Span("Dados detalhados sobre uso de clopidogrel e outros medicamentos cardiovasculares, com verificação cruzada de prescrições.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Representatividade Étnico-Racial: "), style={'color': '#333'}),
                            html.Span("Amostra diversificada refletindo a composição populacional brasileira: 42.7% pardos, 38.5% brancos, 15.6% pretos.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Conformidade Clínica: "), style={'color': '#333'}),
                            html.Span("Dados de comorbidades (hipertensão, diabetes) coletados seguindo protocolos clínicos padronizados.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Contextualização Farmacogenética: "), style={'color': '#333'}),
                            html.Span("Análise integrada de fatores genéticos, demográficos e clínicos para interpretação adequada dos resultados.", style={'color': '#333'})
                        ]),
                    ]
                ),
            ],
        ),

        # Limitações e considerações
        html.Div(
            style={
                'backgroundColor': '#ffffff',
                'padding': '20px',
                'borderRadius': '10px',
                'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                'marginBottom': '40px',
            },
            children=[
                html.H2("Limitações e Considerações de Qualidade", style={'color': '#FF9800'}),
                html.P(
                    "Embora o estudo mantenha altos padrões de qualidade, algumas limitações devem ser consideradas na interpretação dos dados:",
                    style={'color': '#333'}
                ),
                html.Ul(
                    style={'listStyleType': 'disc', 'paddingLeft': '20px'},
                    children=[
                        html.Li([
                            html.Span(html.Strong("Limitação Etária: "), style={'color': '#FF9800'}),
                            html.Span("Embora o ano de nascimento não esteja disponível no estudo, os dados de idade dos participantes permitem realizar análises de estratificação etária. No entanto, a ausência da data completa de nascimento pode limitar interpretações mais refinadas e análises longitudinais.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Dados Educacionais Indisponíveis: "), style={'color': '#FF9800'}),
                            html.Span("A ausência de informações sobre escolaridade pode impactar análises de determinantes sociais da saúde.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Tamanho Amostral Específico: "), style={'color': '#FF9800'}),
                            html.Span("Com 192 participantes, o estudo é adequado para análises farmacogenéticas, mas pode ter limitações para subgrupos muito específicos.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Contexto Temporal: "), style={'color': '#FF9800'}),
                            html.Span("Os dados refletem um período específico e podem não capturar mudanças temporais em protocolos de tratamento.", style={'color': '#333'})
                        ]),
                    ]
                ),
            ],
        ),

        # Dimensões aplicadas
        html.Div(
            style={
                'backgroundColor': '#ffffff',
                'padding': '20px',
                'borderRadius': '10px',
                'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                'marginBottom': '40px',
            },
            children=[
                html.H2("Dimensões de Qualidade Aplicadas", style={'color': '#6200EA'}),
                html.Ul(
                    style={'listStyleType': 'disc', 'paddingLeft': '20px'},
                    children=[
                        html.Li([
                            html.Span(html.Strong("Completude: "), style={'color': '#333'}),
                            html.Span("Verificação da presença de todos os dados esperados para análises farmacogenéticas cardiovasculares.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Conformidade: "), style={'color': '#333'}),
                            html.Span("Aderência a padrões clínicos e farmacológicos estabelecidos para estudos cardiovasculares.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Consistência: "), style={'color': '#333'}),
                            html.Span("Uniformidade entre dados clínicos, farmacológicos e demográficos.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Relevância Clínica: "), style={'color': '#333'}),
                            html.Span("Adequação dos dados para responder questões sobre eficácia e segurança de medicamentos cardiovasculares.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Representatividade Populacional: "), style={'color': '#333'}),
                            html.Span("Reflexo da diversidade étnico-racial brasileira relevante para estudos farmacogenéticos.", style={'color': '#333'})
                        ]),
                    ]
                ),
            ],
        ),

        # Métricas específicas
        html.Div(
            style={
                'backgroundColor': '#ffffff',
                'padding': '20px',
                'borderRadius': '10px',
                'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                'marginBottom': '40px',
            },
            children=[
                html.H2("Métricas de Qualidade Específicas", style={'color': '#6200EA'}),
                html.P(
                    "Para o estudo GS-032, foram implementadas métricas específicas que consideram:",
                    style={'color': '#333'}
                ),
                html.Ul(
                    style={'listStyleType': 'disc', 'paddingLeft': '20px'},
                    children=[
                        html.Li([
                            html.Span(html.Strong("Taxa de Completude por Variável: "), style={'color': '#333'}),
                            html.Span("Monitoramento da presença de dados em cada uma das 134 variáveis coletadas.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Consistência Farmacológica: "), style={'color': '#333'}),
                            html.Span("Verificação da coerência entre uso de medicamentos e indicações clínicas.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Validação de Procedimentos: "), style={'color': '#333'}),
                            html.Span("Confirmação da adequação entre tipo de angioplastia e contexto clínico (eletiva vs emergência).", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Integridade Relacional: "), style={'color': '#333'}),
                            html.Span("Verificação das relações entre comorbidades, medicamentos e procedimentos realizados.", style={'color': '#333'})
                        ]),
                    ]
                ),
            ],
        ),
    ]
)

# Layout principal com a estrutura comum
layout = html.Div(
    className='page-container',
    children=[
        create_navbar_for_route("/dashboard3/farmacogenetica-dimensoes-estudo"),
        html.Div([
            create_sidebar_for_route("/dashboard3/farmacogenetica-dimensoes-estudo"),
            content,
        ], className='main-section', style={'display': 'flex'}),
        footer,
    ],
)

# Callbacks (vazio para esta página estática)
def register_callbacks(app):
    pass
