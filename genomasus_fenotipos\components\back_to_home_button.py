from dash import html, dcc

def create_back_to_home_button():
    return html.Div(
        style={
            'textAlign': 'center',
            'marginTop': '40px',
            'marginBottom': '30px',
            'padding': '20px 0',
        },
        children=[
            dcc.<PERSON>(
                html.But<PERSON>(
                    "Voltar à Página Inicial",
                    style={
                        'backgroundColor': '#007BFF',
                        'color': '#ffffff',
                        'padding': '12px 24px',
                        'border': 'none',
                        'borderRadius': '6px',
                        'fontSize': '16px',
                        'fontWeight': '500',
                        'cursor': 'pointer',
                        'transition': 'all 0.3s ease',
                        'boxShadow': '0 2px 4px rgba(0, 123, 255, 0.2)',
                    },
                    className='back-to-home-btn'
                ),
                href='/',
                style={'textDecoration': 'none'}
            ),
        ]
    )
