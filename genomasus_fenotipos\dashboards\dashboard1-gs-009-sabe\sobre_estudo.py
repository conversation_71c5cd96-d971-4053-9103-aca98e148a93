import pandas as pd
from dash import html, dcc, Dash, Input, Output, State
from components.navbar import create_navbar_for_route
from components.dynamic_sidebar import create_sidebar_for_route
from components.footer import footer
import json
import os

def get_current_palette():
    COLOR_PALETTES = {
        "default": ["#74C0FC", "#339AF0", "#228BE6", "#1971C2", "#1864AB", "#155A9B", "#1C7ED6", "#74C0FC", "#A5D8FF", "#D0EBFF", "#E7F5FF", "#F0F8FF"],
        "warm": ["#FF6B6B", "#FF8E53", "#FF922B", "#FD7E14", "#FAB005", "#FCC419", "#FFD43B", "#FFEC99", "#FFF3BF", "#FFF8DB", "#FFF9DB", "#FFFBF0"],
        "cool": ["#51CF66", "#69DB7C", "#8CE99A", "#B2F2BB", "#C0EB75", "#D8F5A2", "#E9FAB8", "#F4FCE3", "#E6FCF5", "#C3FAE8", "#96F2D7", "#63E6BE"],
        "pastel": ["#FFB6C1", "#FFD700", "#98FB98", "#ADD8E6", "#FFA07A", "#DDA0DD", "#87CEFA", "#F0E68C", "#E6E6FA", "#FFDAB9", "#B0E0E6", "#FFDEAD"]
    }
    
    config_file = 'config/app_config.json'
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        palette_key = config.get('color_palette', 'default')
    else:
        palette_key = 'default'
    
    return COLOR_PALETTES.get(palette_key, COLOR_PALETTES['default'])

# Layout da página
layout = html.Div(
    className='page-container',
    children=[
        html.Link(
            rel='stylesheet',
            href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
        ),
        
        create_navbar_for_route("/dashboard1/sabe-sobre-o-estudo"),
        html.Div(
            className='main-wrapper',
            id='main-wrapper',
            children=[
                create_sidebar_for_route("/dashboard1/sabe-sobre-o-estudo"),
                html.Div(
                    className='content',
                    children=[
                        html.Div(
                            style={
                                'padding': '30px',
                                'max-width': '1000px',
                                'margin': '0 auto',
                                'background-color': '#ffffff',
                                'border-radius': '8px',
                                'box-shadow': '0 2px 10px rgba(0,0,0,0.1)',
                                'margin-top': '20px'
                            },
                            children=[
                                # Título da página
                                html.H1(
                                    children=[
                                        html.Div("Sobre o Estudo GS-009 | SABE", style={'font-weight': '600', 'color': '#74C0FC'}),
                                        html.Div("Saúde, Bem-estar e Envelhecimento", style={'font-size': '0.7em', 'font-weight': '400', 'color': '#666', 'margin-top': '5px'})
                                    ],
                                    style={
                                        'text-align': 'center',
                                        'margin-bottom': '40px',
                                        'padding-bottom': '20px',
                                        'border-bottom': '2px solid #74C0FC'
                                    }
                                ),
                                
                                # Descrição do estudo
                                html.P(
                                    "O Projeto SABE (Saúde, Bem-Estar e Envelhecimento) é uma pesquisa longitudinal e multicêntrica que visa compreender as condições de vida e saúde da população idosa no Brasil e na América Latina. Iniciado em colaboração com a Organização Pan-Americana da Saúde (OPAS/OMS), o estudo investiga fatores como doenças crônicas, nutrição, funcionalidade, cognição, qualidade de vida e aspectos sociais do envelhecimento. No Brasil, o SABE se consolidou como uma das principais fontes de dados sobre envelhecimento populacional, contribuindo para a formulação de políticas públicas, vigilância epidemiológica e ações de promoção à saúde da pessoa idosa. A metodologia combina entrevistas domiciliares, avaliações clínicas, testes funcionais e biomarcadores. O estudo permite acompanhar mudanças ao longo do tempo por meio de coortes sequenciais, gerando evidências robustas para enfrentar os desafios do envelhecimento em contextos urbanos. Os dados coletados são amplamente utilizados por pesquisadores, gestores e instituições de saúde pública.",
                                    style={
                                        'text-align': 'justify',
                                        'line-height': '1.6',
                                        'font-size': '16px',
                                        'color': '#333',
                                        'margin-bottom': '30px'
                                    }
                                ),
                                
                                # Botão de volta
                                html.Div(
                                    style={'text-align': 'center', 'margin-top': '40px'},
                                    children=[
                                        dcc.Link(
                                            html.Button(
                                                "Voltar aos Dashboards",
                                                style={
                                                    'background-color': '#74C0FC',
                                                    'color': '#ffffff',
                                                    'padding': '12px 24px',
                                                    'border': 'none',
                                                    'border-radius': '6px',
                                                    'font-size': '16px',
                                                    'font-weight': '500',
                                                    'cursor': 'pointer',
                                                    'transition': 'all 0.3s ease',
                                                    'box-shadow': '0 2px 4px rgba(116, 192, 252, 0.2)',
                                                }
                                            ),
                                            href='/dashboard1/sabe-sociodemografico',
                                            style={'text-decoration': 'none'}
                                        )
                                    ]
                                )
                            ]
                        )
                    ],
                ),
            ],
        ),
        footer,
    ],
)

def register_callbacks(app):
    pass
