#!/usr/bin/env python3

from dash import html, dcc
from components.navbar import create_navbar_for_route
from components.dynamic_sidebar import create_sidebar_for_route
from components.footer import footer

# Conteúdo da página de dimensões de qualidade
content = html.Div(
    className='content',
    style={
        'padding': '40px 20px',
        'maxWidth': '1200px',
        'margin': '0 auto',
        'lineHeight': '1.8',
        'color': '#333',
        'backgroundColor': '#f9f9f9',
        'fontFamily': 'Arial, sans-serif',
    },
    children=[
        # Título principal
        html.H1(
            "Dimensões de Qualidade – Estudo GS-004 Renomica-DAC",
            style={
                'textAlign': 'center',
                'color': '#b7d5ac',
                'marginBottom': '40px',
                'fontSize': 'clamp(24px, 5vw, 36px)',
                'fontWeight': '600',
            }
        ),

        # Descrição específica do estudo
        html.Div(
            style={
                'backgroundColor': '#ffffff',
                'padding': '20px',
                'borderRadius': '10px',
                'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                'marginBottom': '40px',
            },
            children=[
                html.P(
                    "O estudo GS-004 Renomica-DAC (Rede Nacional de Genômica Cardiovascular – Doença Arterial Coronariana) representa um marco na pesquisa cardiovascular brasileira, focando especificamente em pacientes com doença arterial coronariana confirmada por cateterismo. Com 137 participantes e 133 variáveis analisadas, este estudo fornece dados fundamentais sobre características fenotípicas cardiovasculares e metabólicas. A qualidade dos dados é essencial para garantir a validade das análises genômicas e a aplicabilidade clínica dos resultados no contexto do Sistema Único de Saúde (SUS).",
                    style={'color': '#333'}
                ),
                html.P(
                    "As dimensões de qualidade aplicadas ao estudo Renomica-DAC consideram as especificidades da pesquisa cardiovascular, incluindo a precisão dos dados clínicos, a consistência das informações antropométricas e laboratoriais, e a representatividade da amostra em relação à população brasileira com doença arterial coronariana. Com uma taxa de completude geral de 64.0% após rigoroso processo de limpeza e validação, o estudo demonstra qualidade adequada para análises robustas em genômica cardiovascular.",
                    style={'color': '#333'}
                ),
            ],
        ),

        # Características específicas do estudo
        html.Div(
            style={
                'backgroundColor': '#ffffff',
                'padding': '20px',
                'borderRadius': '10px',
                'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                'marginBottom': '40px',
            },
            children=[
                html.H2("Características de Qualidade do Estudo GS-004 Renomica-DAC", style={'color': '#b7d5ac'}),
                html.Ul(
                    style={'listStyleType': 'disc', 'paddingLeft': '20px'},
                    children=[
                        html.Li([
                            html.Span(html.Strong("Completude Cardiovascular: "), style={'color': '#333'}),
                            html.Span("137 participantes com doença arterial coronariana confirmada por cateterismo, garantindo homogeneidade clínica.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Consistência Antropométrica: "), style={'color': '#333'}),
                            html.Span("Dados de altura, peso e IMC validados e corrigidos, com valores biologicamente plausíveis (IMC médio 27.6 kg/m²).", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Representatividade Étnico-Racial: "), style={'color': '#333'}),
                            html.Span("Amostra diversificada refletindo a composição populacional brasileira: 47% pardos, 38% brancos, 12% pretos.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Conformidade Clínica: "), style={'color': '#333'}),
                            html.Span("Dados de comorbidades (hipertensão 65%, diabetes 38%) coletados seguindo protocolos cardiológicos padronizados.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Contextualização Genômica: "), style={'color': '#333'}),
                            html.Span("Análise integrada de fatores fenotípicos cardiovasculares para interpretação adequada de dados genômicos.", style={'color': '#333'})
                        ]),
                    ]
                ),
            ],
        ),

        # Limitações e considerações
        html.Div(
            style={
                'backgroundColor': '#ffffff',
                'padding': '20px',
                'borderRadius': '10px',
                'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                'marginBottom': '40px',
            },
            children=[
                html.H2("Limitações e Considerações de Qualidade", style={'color': '#FF9800'}),
                html.P(
                    "Embora o estudo Renomica-DAC mantenha altos padrões de qualidade, algumas limitações devem ser consideradas na interpretação dos dados:",
                    style={'color': '#333'}
                ),
                html.Ul(
                    style={'listStyleType': 'disc', 'paddingLeft': '20px'},
                    children=[
                        html.Li([
                            html.Span(html.Strong("Códigos de Missing Tratados: "), style={'color': '#FF9800'}),
                            html.Span("Valores 888 e 999 foram convertidos para NaN, reduzindo a completude mas aumentando a precisão analítica.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Outliers Biológicos Removidos: "), style={'color': '#FF9800'}),
                            html.Span("Valores extremos de altura e peso foram corrigidos, podendo afetar a variabilidade natural da amostra.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Tamanho Amostral Específico: "), style={'color': '#FF9800'}),
                            html.Span("Com 137 participantes, o estudo é adequado para análises genômicas, mas pode ter limitações para subgrupos muito específicos.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("População Específica: "), style={'color': '#FF9800'}),
                            html.Span("Foco em pacientes com DAC confirmada limita a generalização para população geral ou outras cardiopatias.", style={'color': '#333'})
                        ]),
                    ]
                ),
            ],
        ),

        # Dimensões aplicadas
        html.Div(
            style={
                'backgroundColor': '#ffffff',
                'padding': '20px',
                'borderRadius': '10px',
                'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                'marginBottom': '40px',
            },
            children=[
                html.H2("Dimensões de Qualidade Aplicadas", style={'color': '#b7d5ac'}),
                html.Ul(
                    style={'listStyleType': 'disc', 'paddingLeft': '20px'},
                    children=[
                        html.Li([
                            html.Span(html.Strong("Completude: "), style={'color': '#333'}),
                            html.Span("Verificação da presença de todos os dados esperados para análises genômicas cardiovasculares.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Conformidade: "), style={'color': '#333'}),
                            html.Span("Aderência a padrões clínicos e laboratoriais estabelecidos para estudos de doença arterial coronariana.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Consistência: "), style={'color': '#333'}),
                            html.Span("Uniformidade entre dados antropométricos, clínicos e laboratoriais após processo de validação.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Relevância Clínica: "), style={'color': '#333'}),
                            html.Span("Adequação dos dados para responder questões sobre genômica cardiovascular e medicina de precisão.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Representatividade Populacional: "), style={'color': '#333'}),
                            html.Span("Reflexo da diversidade étnico-racial brasileira relevante para estudos genômicos.", style={'color': '#333'})
                        ]),
                    ]
                ),
            ],
        ),

        # Métricas específicas
        html.Div(
            style={
                'backgroundColor': '#ffffff',
                'padding': '20px',
                'borderRadius': '10px',
                'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                'marginBottom': '40px',
            },
            children=[
                html.H2("Métricas de Qualidade Específicas", style={'color': '#b7d5ac'}),
                html.P(
                    "Para o estudo GS-004 Renomica-DAC, foram implementadas métricas específicas que consideram:",
                    style={'color': '#333'}
                ),
                html.Ul(
                    style={'listStyleType': 'disc', 'paddingLeft': '20px'},
                    children=[
                        html.Li([
                            html.Span(html.Strong("Taxa de Completude por Variável: "), style={'color': '#333'}),
                            html.Span("Monitoramento da presença de dados em cada uma das 133 variáveis coletadas (64.0% pós-limpeza).", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Consistência Antropométrica: "), style={'color': '#333'}),
                            html.Span("Verificação da coerência entre altura, peso e IMC calculado automaticamente.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Validação Temporal: "), style={'color': '#333'}),
                            html.Span("Confirmação da adequação entre ano de nascimento e idade reportada pelos participantes.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Integridade Cardiovascular: "), style={'color': '#333'}),
                            html.Span("Verificação das relações entre comorbidades, fatores de risco e diagnóstico de DAC.", style={'color': '#333'})
                        ]),
                    ]
                ),
            ],
        ),

        # Outras Métricas
        html.Div(
            style={
                'backgroundColor': '#ffffff',
                'padding': '20px',
                'borderRadius': '10px',
                'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                'marginBottom': '40px',
            },
            children=[
                html.H2("Outras Métricas"),
                html.P(
                    "O projeto também abrange métricas complementares, agrupadas em:",
                    style={'color': '#333'}
                ),
                html.Ul(
                    style={'listStyleType': 'disc', 'paddingLeft': '20px'},
                    children=[
                        html.Li([
                            html.Span(html.Strong("Métricas Aglutinadas: "), style={'color': '#333'}),
                            html.Span("Englobam aspectos como captura, unicidade, comparabilidade e interoperabilidade, que reforçam as dimensões primárias.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Métricas Estruturais (Técnicas): "), style={'color': '#333'}),
                            html.Span("Referem-se à segurança, acessibilidade, atualidade, flexibilidade, imediatismo, integridade relacional e rastreabilidade dos dados.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Métricas Longitudinais: "), style={'color': '#333'}),
                            html.Span("Envolvem a atemporalidade, temporalidade e estabilidade dos dados, essenciais para análises que acompanham sua evolução ao longo do tempo.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Métricas para um Segundo Momento: "), style={'color': '#333'}),
                            html.Span("Abrangem conceitos como artificialidade, inteligibilidade, validade, confiabilidade e plausibilidade, complementando a avaliação global dos dados.", style={'color': '#333'})
                        ]),
                    ]
                ),
            ],
        ),
    ]
)

# Layout principal com a estrutura comum
layout = html.Div(
    className='page-container',
    children=[
        create_navbar_for_route("/dashboard2/renomica-dimensoes-estudo-renomicadac"),
        html.Div([
            create_sidebar_for_route("/dashboard2/renomica-dimensoes-estudo-renomicadac"),
            content,
        ], className='main-section', style={'display': 'flex'}),
        footer,
    ],
)

# Callbacks (vazio para esta página estática)
def register_callbacks(app):
    pass
