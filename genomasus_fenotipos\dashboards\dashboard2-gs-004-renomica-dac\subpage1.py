import pandas as pd
from dash import html, dcc, Input, Output
from components.navbar import create_navbar_for_route
from components.dynamic_sidebar import create_sidebar_for_route
from components.footer import footer
import plotly.express as px
import json
import os
from . import DATA_FILE

def get_current_palette():
    # Paletas de cores definidas
    COLOR_PALETTES = {
        "default": ["#6200EA", "#BB86FC", "#03DAC6", "#018786", "#FFEB3B", "#FFC107", "#FF9800", "#F44336", "#E91E63", "#9C27B0", "#673AB7", "#3F51B5"],
        "colorblind": ["#000000", "#E69F00", "#56B4E9", "#009E73", "#F0E442", "#0072B2", "#D55E00", "#CC79A7", "#999999", "#332288", "#44AA99", "#882255"],
        "autistic": ["#7F95D1", "#7FC9D1", "#7FD195", "#D1C97F", "#D17F95", "#957FD1", "#D1957F", "#95D17F", "#7F95D1", "#7FBCD1", "#D17F95", "#957FD1"],
        "monochrome": ["#0D47A1", "#1565C0", "#1976D2", "#1E88E5", "#2196F3", "#42A5F5", "#64B5F6", "#90CAF9", "#BBDEFB", "#E3F2FD", "#2962FF", "#0D47A1"],
        "pastel": ["#FFB6C1", "#FFD700", "#98FB98", "#ADD8E6", "#FFA07A", "#DDA0DD", "#87CEFA", "#F0E68C", "#E6E6FA", "#FFDAB9", "#B0E0E6", "#FFDEAD"]
    }

    config_file = 'config/app_config.json'
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        palette_key = config.get('color_palette', 'default')
    else:
        palette_key = 'default'

    return COLOR_PALETTES.get(palette_key, COLOR_PALETTES['default'])

# Carregamento e processamento dos dados
df = pd.read_csv(DATA_FILE)

# Criar idade a partir do ano de nascimento
current_year = 2025
df['idade'] = current_year - df['qual_o_seu_ano_de_nascimento']
df = df[df['idade'].between(0, 110)]

# Categorizar idade
def categorize_age(age):
    if pd.isna(age):
        return 'Não informado'
    elif age < 30:
        return '< 30 anos'
    elif age < 40:
        return '30-39 anos'
    elif age < 50:
        return '40-49 anos'
    elif age < 60:
        return '50-59 anos'
    elif age < 70:
        return '60-69 anos'
    elif age < 80:
        return '70-79 anos'
    else:
        return '≥ 80 anos'

df['idade_categoria'] = df['idade'].apply(categorize_age)

# Criar variáveis binárias para sexo
df['sexo_feminino'] = (df['qual_o_seu_sexo_biologico'] == 'Feminino').astype(int)

# Processar variáveis de hipertensão - USAR VARIÁVEL JÁ PROCESSADA DO DATASET
# A variável 'tem_hipertensao' já existe no dataset com os valores corretos (89.1% de prevalência)
# Não recriar para manter a integridade estatística dos dados originais

# Filtrar registros com informações válidas apenas para análises que requerem essas variáveis específicas
# Manter dataset completo para cálculos de prevalência geral
df_filtered_for_analysis = df[df['qual_o_seu_sexo_biologico'].notna() &
                              df['algum_profissional_de_saude_ja_disse_que_voce_tem_pressao_alta_ou_hipertensao'].notna()]
df['idade_diagnostico_hipertensao'] = df['que_idade_voce_tinha_quando_te_disseram_isso_pela_primeira_vez']

# Processar medicamento para hipertensão
df['tomou_remedio_hipertensao_sim_agora'] = (df['alguma_vez_voce_ja_tomou_remedio_para_pressao_alta_ou_hipertensao'] == 'Sim, agora').astype(int)
df['tomou_remedio_hipertensao_sim_mas_nao_agora'] = (df['alguma_vez_voce_ja_tomou_remedio_para_pressao_alta_ou_hipertensao'] == 'Sim, mas não agora').astype(int)
df['tomou_remedio_hipertensao_nao'] = (df['alguma_vez_voce_ja_tomou_remedio_para_pressao_alta_ou_hipertensao'] == 'Não').astype(int)

def create_graph(title, figure, palette_colors=None):
    if palette_colors is None:
        palette_colors = get_current_palette()

    if 'data' in figure and figure['data']:
        for i, trace in enumerate(figure['data']):
            if 'marker' in trace:
                if 'color' in trace['marker'] and isinstance(trace['marker']['color'], str):
                    color_index = i % len(palette_colors)
                    trace['marker']['color'] = palette_colors[color_index]
                elif 'colors' in trace['marker'] and isinstance(trace['marker']['colors'], list):
                    num_colors = len(trace['marker']['colors'])
                    trace['marker']['colors'] = palette_colors[:num_colors]

    return html.Div([
        html.H3(title, style={'textAlign': 'center'}),
        dcc.Graph(figure=figure)
    ], className='graph-container')

# Layout da subpágina de Hipertensão
content = html.Div([
    html.H1("Estatísticas Hipertensão - RENOMICA-DAC (GS-004)", style={'textAlign': 'center'}),

    # Filtros globais
    html.Div([
        html.Label("Filtrar por Sexo:", style={'marginRight': '10px'}),
        dcc.Dropdown(
            id='sexo-filter-subpage1-dashboard2',
            options=[{'label': 'Todos', 'value': 'all'}] +
                    [{'label': sexo, 'value': sexo} for sexo in ['Masculino', 'Feminino']],
            value='all',
            clearable=False,
            style={'width': '200px'}
        ),
        html.Label("Filtrar por Raça/Cor:", style={'marginLeft': '20px', 'marginRight': '10px'}),
        dcc.Dropdown(
            id='raca-filter-subpage1-dashboard2',
            options=[{'label': 'Todos', 'value': 'all'}] +
                    [{'label': raca, 'value': raca} for raca in df['como_voce_se_considera'].dropna().unique()],
            value='all',
            clearable=False,
            style={'width': '200px'}
        )
    ], style={'display': 'flex', 'justifyContent': 'center', 'marginBottom': '20px'}),

    # Gráficos base (serão atualizados dinamicamente)
    html.Div(id='dynamic-graphs-subpage1-dashboard2')
], className='content')

# Layout principal com a estrutura comum
layout = html.Div(
    className='page-container',
    children=[
        create_navbar_for_route("/dashboard2/renomica-hipertensao"),
        html.Div([
            create_sidebar_for_route("/dashboard2/renomica-hipertensao"),
            content,
        ], className='main-section', style={'display': 'flex'}),
        footer,
    ],
)

# Callbacks
def register_callbacks(app):
    @app.callback(
        Output('dynamic-graphs-subpage1-dashboard2', 'children'),
        [Input('sexo-filter-subpage1-dashboard2', 'value'), Input('raca-filter-subpage1-dashboard2', 'value')]
    )
    def update_graphs(sexo_filter, raca_filter):
        palette_colors = get_current_palette()

        # Usar dataset completo para cálculos de prevalência (mantém 89.1% correto)
        filtered_df = df.copy()
        if sexo_filter != 'all':
            filtered_df = filtered_df[filtered_df['qual_o_seu_sexo_biologico'] == sexo_filter]

        if raca_filter != 'all':
            filtered_df = filtered_df[filtered_df['como_voce_se_considera'] == raca_filter]

        # Pergunta 13: Prevalência da hipertensão no estudo - USAR DADOS VÁLIDOS APENAS
        # Filtrar apenas registros com dados válidos para esta análise específica
        valid_hypertension_data = filtered_df[filtered_df['tem_hipertensao'].notna()]
        hipertensao_counts = valid_hypertension_data['tem_hipertensao'].value_counts()
        labels_hipertensao = ['Não Hipertenso', 'Hipertenso']
        graph_13 = create_graph("Prevalência da Hipertensão no Estudo", {
            'data': [{
                'labels': [labels_hipertensao[i] for i in hipertensao_counts.index],
                'values': hipertensao_counts.values.tolist(),
                'type': 'pie',
                'marker': {'colors': [palette_colors[0], palette_colors[1]]}
            }],
            'layout': {'title': 'Prevalência da Hipertensão'}
        }, palette_colors)

        # Pergunta 14: Prevalência da hipertensão pela distribuição etária e sexo
        graph_14 = create_graph("Prevalência da Hipertensão por Idade e Sexo", {
            'data': [
                {
                    'x': filtered_df[(filtered_df['qual_o_seu_sexo_biologico'] == 'Masculino') & (filtered_df['tem_hipertensao'] == 1)]['idade'],
                    'name': 'Masculino Hipertenso',
                    'type': 'histogram',
                    'marker': {'color': '#4472C4'},  # Azul sólido
                    'nbinsx': 15
                },
                {
                    'x': filtered_df[(filtered_df['qual_o_seu_sexo_biologico'] == 'Feminino') & (filtered_df['tem_hipertensao'] == 1)]['idade'],
                    'name': 'Feminino Hipertenso',
                    'type': 'histogram',
                    'marker': {'color': '#E15759'},  # Vermelho sólido
                    'nbinsx': 15
                }
            ],
            'layout': {
                'barmode': 'group',
                'title': 'Prevalência da Hipertensão por Idade e Sexo',
                'xaxis': {'title': 'Idade (anos)'},
                'yaxis': {'title': 'Número de hipertensos'},
                'legend': {'orientation': 'h', 'y': -0.15},
                'bargap': 0.1
            }
        }, palette_colors)

        # Pergunta 16: Distribuição de idades em que houve diagnóstico de hipertensão
        idades_diagnostico = filtered_df[filtered_df['tem_hipertensao'] == 1]['idade_diagnostico_hipertensao'].dropna()
        graph_16 = create_graph("Distribuição de Idades com Diagnóstico de Hipertensão", {
            'data': [{'x': idades_diagnostico, 'type': 'histogram', 'marker': {'color': palette_colors[3]}, 'nbinsx': 15}],
            'layout': {'title': 'Idades no Diagnóstico de Hipertensão', 'xaxis': {'title': 'Idade no diagnóstico (anos)'}, 'yaxis': {'title': 'Número de casos'}}
        }, palette_colors)

        # Pergunta 17: Distribuição das pessoas que possuem diagnóstico de hipertensão e tomam medicamento
        medicamento_data = []
        medicamento_labels = ['Não toma', 'Sim, agora', 'Sim, mas não agora']
        medicamento_values = [
            filtered_df[(filtered_df['tem_hipertensao'] == 1) & (filtered_df['tomou_remedio_hipertensao_nao'] == 1)].shape[0],
            filtered_df[(filtered_df['tem_hipertensao'] == 1) & (filtered_df['tomou_remedio_hipertensao_sim_agora'] == 1)].shape[0],
            filtered_df[(filtered_df['tem_hipertensao'] == 1) & (filtered_df['tomou_remedio_hipertensao_sim_mas_nao_agora'] == 1)].shape[0]
        ]

        graph_17 = create_graph("Pessoas com Hipertensão que Tomam Medicamento", {
            'data': [{
                'labels': medicamento_labels,
                'values': medicamento_values,
                'type': 'pie',
                'marker': {'colors': palette_colors[:3]}
            }],
            'layout': {'title': 'Uso de Medicamento para Hipertensão'}
        }, palette_colors)

        # Gráfico 1: Prevalência da Hipertensão por Sexo (estilo Farmacogenética)
        sexo_hyp_data = []
        sexos = ['Masculino', 'Feminino']
        sexo_values = [0, 1]

        for i, (sexo, value) in enumerate(zip(sexos, sexo_values)):
            subset = filtered_df[filtered_df['sexo_feminino'] == value]
            total = len(subset)
            hipertensos = subset['tem_hipertensao'].sum()
            prevalencia = (hipertensos / total * 100) if total > 0 else 0

            sexo_hyp_data.append({
                'x': [sexo],
                'y': [prevalencia],
                'type': 'bar',
                'name': sexo,
                'marker': {'color': palette_colors[i]},
                'text': [f'{hipertensos}/{total}'],
                'textposition': 'auto'
            })

        graph_sexo_hyp = create_graph("Prevalência da Hipertensão por Sexo", {
            'data': sexo_hyp_data,
            'layout': {
                'title': 'Prevalência da Hipertensão por Sexo',
                'xaxis': {'title': 'Sexo'},
                'yaxis': {'title': 'Prevalência (%)', 'tickformat': '.1f', 'ticksuffix': '%'},
                'showlegend': False
            }
        }, palette_colors)

        # Gráfico 2: Prevalência de Hipertensão por Idade e Sexo (gráfico de barras agrupadas)
        graph_idade_sexo_barras = create_graph("Prevalência de Hipertensão por Idade e Sexo", {
            'data': [
                {
                    'x': filtered_df[(filtered_df['sexo_feminino'] == 0) & (filtered_df['tem_hipertensao'] == 1)]['idade'],
                    'name': 'Masculino Hipertenso',
                    'type': 'histogram',
                    'marker': {'color': '#4472C4'},  # Azul sólido
                    'nbinsx': 15
                },
                {
                    'x': filtered_df[(filtered_df['sexo_feminino'] == 1) & (filtered_df['tem_hipertensao'] == 1)]['idade'],
                    'name': 'Feminino Hipertenso',
                    'type': 'histogram',
                    'marker': {'color': '#E15759'},  # Vermelho sólido
                    'nbinsx': 15
                }
            ],
            'layout': {
                'barmode': 'group',
                'title': 'Prevalência de Hipertensão por Idade e Sexo',
                'xaxis': {'title': 'Idade (anos)'},
                'yaxis': {'title': 'Número de hipertensos'},
                'legend': {'orientation': 'h', 'y': -0.15},
                'bargap': 0.1
            }
        }, palette_colors)

        # Gráfico 3: Prevalência da Hipertensão por Faixa Etária (estilo Farmacogenética)
        idade_hyp_data = []
        idade_order = ['< 30 anos', '30-39 anos', '40-49 anos', '50-59 anos', '60-69 anos', '70-79 anos', '≥ 80 anos']

        for i, categoria in enumerate(idade_order):
            if categoria in filtered_df['idade_categoria'].values:
                subset = filtered_df[filtered_df['idade_categoria'] == categoria]
                total = len(subset)
                prevalencia = (subset['tem_hipertensao'].sum() / total * 100) if total > 0 else 0

                if total > 0:
                    idade_hyp_data.append({
                        'x': [categoria],
                        'y': [prevalencia],
                        'type': 'bar',
                        'marker': {'color': palette_colors[i % len(palette_colors)]},
                        'name': categoria,
                        'showlegend': False
                    })

        graph_idade_hyp = create_graph("Prevalência da Hipertensão por Faixa Etária", {
            'data': idade_hyp_data,
            'layout': {
                'title': 'Prevalência da Hipertensão por Faixa Etária',
                'xaxis': {'title': 'Faixa Etária'},
                'yaxis': {'title': 'Prevalência (%)', 'tickformat': '.1f', 'ticksuffix': '%'},
                'showlegend': False
            }
        }, palette_colors)

        return html.Div([
            html.Div([graph_13, graph_14], className='row'),
            html.Div([graph_16, graph_17], className='row'),
            html.Div([graph_sexo_hyp, graph_idade_sexo_barras], className='row'),
            html.Div([graph_idade_hyp], className='row')
        ])