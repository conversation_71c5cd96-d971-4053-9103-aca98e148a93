from dash import html, dcc
from components.back_to_home_button import create_back_to_home_button

# Descritivo sobre o projeto GenomaSUS
texto_descricao = [
    html.H2("🧬 GenomaSUS – Genômica e Saúde de Precisão no SUS", style={'color': '#007BFF', 'marginBottom': '20px'}),
    html.P(
        "O GenomaSUS é uma iniciativa estratégica do Ministério da Saúde, criada no âmbito do Programa Nacional de Genômica e Saúde de Precisão – Genomas Brasil. Seu principal objetivo é integrar a genômica à saúde pública brasileira, promovendo o desenvolvimento de soluções diagnósticas, preditivas e terapêuticas personalizadas no Sistema Único de Saúde (SUS).",
        style={'color': '#333', 'marginBottom': '15px'}
    ),
    html.P(
        "A proposta do projeto é realizar o sequenciamento genético de aproximadamente 21 mil brasileiros, com atenção especial à representatividade étnica, regional e populacional do país. Esse esforço visa formar uma base nacional de dados genômicos integrada a informações clínicas e epidemiológicas, permitindo a identificação de variantes genéticas associadas à predisposição, evolução e resposta a diversas doenças.",
        style={'color': '#333', 'marginBottom': '15px'}
    ),
    html.P(
        "A estrutura do GenomaSUS conta com uma rede de centros distribuídos em todas as regiões do Brasil, responsáveis pela coleta de amostras, processamento genômico e análise dos dados, garantindo abrangência e diversidade genética. Além disso, o projeto impulsiona a formação de profissionais especializados em bioinformática, ciência de dados, genômica clínica e saúde de precisão, fortalecendo a capacidade técnica nacional.",
        style={'color': '#333', 'marginBottom': '15px'}
    ),
    html.P(
        "O banco de dados gerado pelo GenomaSUS servirá de base para o desenvolvimento de políticas públicas mais eficazes, contribuindo para melhorar a prevenção, o diagnóstico e o tratamento de doenças no SUS, com foco na equidade, eficiência e inovação. Estão previstos investimentos contínuos e a ampliação do número de genomas sequenciados nos próximos anos, consolidando o Brasil como referência em saúde de precisão na América Latina.",
        style={'color': '#333', 'marginBottom': '15px'}
    ),
    html.P(
        "O GenomaSUS representa, portanto, um marco para a saúde pública brasileira ao viabilizar a aplicação da genômica em larga escala e de forma estruturada, promovendo um modelo de atenção à saúde mais moderno, integrado e personalizado.",
        style={'color': '#333', 'marginBottom': '20px'}
    ),
]

# Layout da página "Sobre"
layout = html.Div(
    style={
        'padding': '40px 20px',
        'maxWidth': '1200px',
        'margin': '0 auto',
        'lineHeight': '1.8',
        'color': '#333',
        'backgroundColor': '#f9f9f9',
        'fontFamily': 'Arial, sans-serif',
    },
    children=[
        # Título principal
        html.H1(
            "Sobre o Projeto GenomaSUS",
            style={
                'textAlign': 'center',
                'color': '#007BFF',
                'marginBottom': '20px',
                'fontSize': 'clamp(24px, 5vw, 36px)',
                'fontWeight': '600',
            }
        ),

        # Descrição do projeto
        html.Div(
            style={
                'backgroundColor': '#ffffff',
                'padding': '20px',
                'borderRadius': '10px',
                'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                'marginBottom': '40px',
            },
            children=texto_descricao,
        ),

        # Botão padronizado "Voltar à Página Inicial"
        create_back_to_home_button(),

        # Rodapé
        html.Footer(
            style={
                'marginTop': '60px',
                'padding': '20px',
                'backgroundColor': '#f1f1f1',  
                'color': '#000',  
                'textAlign': 'center',
            },
            children=[
                html.P("© 2025 Projeto GenomaSUS - Fenótipos. Todos os direitos reservados.",
                style={'color': '#333'}),
                html.P(
                    "Contato: ",
                    style={'marginTop': '10px', 'color': '#333'}
                ),
                dcc.Link(
                    "<EMAIL>",
                    href="mailto:<EMAIL>",
                    style={'color': '#007BFF', 'textDecoration': 'none'} 
                ),
            ]
        ),
    ]
)