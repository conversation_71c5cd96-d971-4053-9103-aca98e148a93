/* assets/styles.css */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap');

:root {
  --primary-color: #74C0FC;
  --secondary-color: #292A2D;
  --background-color: #FAFAFA;
  --text-color: #212327;
  --light-text-color: #F1F1F1;
  --font-family: 'Poppins', sans-serif;
}

/* Global styles */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  margin: 0;
  padding: 0;
  background-color: var(--background-color);
  overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family);
  font-weight: 600;
  color: var(--secondary-color);
  margin-top: 0;
}

h1 {
  font-size: clamp(20px, 5vw, 24px);
}

p, a, span, li {
  font-family: var(--font-family);
  font-weight: 400;
  color: var(--light-text-color);
}

a {
  text-decoration: none;
  color: var(--light-text-color);
  transition: color 0.3s ease;
}

a:hover {
  color: var(--background-color);
}


.container-404 {
    text-align: center;
    margin-top: 10%;
    padding: 20px;
    font-family: var(--font-family)
}

.title-404 {
    font-size: 38px;
    color: #2c3e50;
    margin-bottom: 20px;
}

.message-404 {
    font-size: 18px;
    color: #7f8c8d;
    margin-bottom: 40px;
}

.link-404 {
    text-decoration: none;
    background-color: var(--primary-color);
    color: white;
    padding: 15px 30px;
    border-radius: 5px;
    font-size: 18px;
    transition: background-color 0.3s ease;
}

.link-404:hover {
    background-color: #2980b9;
}