# dashboards/configuracoes/main.py
import dash
from dash import html, dcc, callback, Input, Output, State
from components.navbar import create_navbar
from components.sidebar import create_sidebar
from components.footer import footer
import plotly.express as px
import plotly.graph_objects as go
import json
import os

# Definição das paletas de cores
COLOR_PALETTES = {
    "default": {
        "name": "Padrão",
        "description": "Paleta padrão de cores do sistema, com combinações variadas e contraste balanceado para uso geral.",
        "colors": ["#6200EA", "#BB86FC", "#03DAC6", "#018786", "#FFEB3B", "#FFC107", "#FF9800", "#F44336", "#E91E63", "#9C27B0", "#673AB7", "#3F51B5"]
    },
    "colorblind": {
        "name": "Baixa Percepção de Cores",
        "description": "Paleta desenvolvida para garantir contraste e distinção entre cores por pessoas com deficiência na percepção de cores (como daltonismo).",
        "colors": ["#000000", "#E69F00", "#56B4E9", "#009E73", "#F0E442", "#0072B2", "#D55E00", "#CC79A7", "#999999", "#332288", "#44AA99", "#882255"]
    },
    "autistic": {
        "name": "Baixo Estímulo Visual",
        "description": "Paleta com menor contraste e tons mais suaves, ideal para pessoas com sensibilidade visual ou que se beneficiam de estímulos visuais reduzidos.",
        "colors": ["#7F95D1", "#7FC9D1", "#7FD195", "#D1C97F", "#D17F95", "#957FD1", "#D1957F", "#95D17F", "#7F95D1", "#7FBCD1", "#D17F95", "#957FD1"]
    },
    "monochrome": {
        "name": "Monocromático",
        "description": "Paleta composta por diferentes tonalidades de azul, oferecendo consistência visual e facilitando a leitura em ambientes com baixa distração.",
        "colors": ["#0D47A1", "#1565C0", "#1976D2", "#1E88E5", "#2196F3", "#42A5F5", "#64B5F6", "#90CAF9", "#BBDEFB", "#E3F2FD", "#2962FF", "#0D47A1"]
    },
    "pastel": {
        "name": "Tons Pastéis",
        "description": "Paleta com cores suaves e agradáveis, ideal para visualizações que priorizam leveza visual e estética delicada.",
        "colors": ["#FFB6C1", "#FFD700", "#98FB98", "#ADD8E6", "#FFA07A", "#DDA0DD", "#87CEFA", "#F0E68C", "#E6E6FA", "#FFDAB9", "#B0E0E6", "#FFDEAD"]
    }
}

# Layout da página de configurações
layout = html.Div(
    className='page-container',
    children=[
        # Adicionar o css para o Font Awesome
        html.Link(
            rel='stylesheet',
            href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
        ),
        
        create_navbar("default"),
        html.Div(
            className='main-wrapper',
            id='main-wrapper',
            children=[
                create_sidebar("config_only"),
                html.Div(
                    className='content',
                    children=[
                        html.Div(
                            id='configuracoes-content',
                            children=[
                                html.H1("Configurações de Visualização", style={'textAlign': 'center'}),
                                html.Div(
                                    className='config-container',
                                    style={
                                        'maxWidth': '800px', 
                                        'margin': '0 auto', 
                                        'padding': '20px',
                                        'backgroundColor': '#fff',
                                        'borderRadius': '8px',
                                        'boxShadow': '0 2px 10px rgba(0, 0, 0, 0.1)'
                                    },
                                    children=[
                                        html.H2("Paletas de Cores", style={'marginBottom': '20px'}),
                                        html.P(
                                            "Selecione uma paleta de cores para adaptar a visualização do dashboard conforme suas preferências visuais. "
                                            "As opções incluem variações acessíveis que respeitam diferentes formas de percepção e sensibilidade, "
                                            "tornando a navegação mais inclusiva.",
                                            style={
                                                'marginBottom': '30px',
                                                'color': 'black',
                                            }
                                        ),
                                        
                                        # Paleta atual
                                        html.Div(
                                            style={'marginBottom': '30px'},
                                            children=[
                                                html.H3("Paleta Atual:", style={'marginBottom': '10px'}),
                                                html.Div(
                                                    id='current-palette-name',
                                                    style={'fontWeight': 'bold', 'fontSize': '18px', 'marginBottom': '10px'}
                                                ),
                                                html.Div(
                                                    id='current-palette-display',
                                                    style={
                                                        'display': 'flex',
                                                        'flexWrap': 'wrap',
                                                        'gap': '10px',
                                                        'marginBottom': '20px'
                                                    }
                                                ),
                                            ]
                                        ),
                                        
                                        # Seleção de paleta
                                        html.Label(
                                            "Selecione uma paleta:",
                                            style={'fontWeight': 'bold', 'marginBottom': '10px', 'display': 'block'}
                                        ),
                                        dcc.Dropdown(
                                            id='palette-selector',
                                            options=[
                                                {'label': data['name'], 'value': key}
                                                for key, data in COLOR_PALETTES.items()
                                            ],
                                            value='default',
                                            clearable=False,
                                            style={'marginBottom': '20px'}
                                        ),
                                        
                                        # Descrição da paleta
                                        html.Div(
                                            id='palette-description',
                                            style={'marginBottom': '20px', 'fontStyle': 'italic'}
                                        ),
                                        
                                        # Exemplo de visualização
                                        html.H3("Exemplo de Visualização", style={'marginTop': '30px', 'marginBottom': '15px'}),
                                        html.Div([
                                            dcc.Graph(id='palette-example-pie'),
                                            dcc.Graph(id='palette-example-bar')
                                        ], style={'display': 'flex', 'flexWrap': 'wrap', 'gap': '20px', 'justifyContent': 'center'}),
                                        
                                        # Botão de aplicar configurações
                                        html.Div(
                                            style={'textAlign': 'center', 'marginTop': '30px'},
                                            children=[
                                                html.Button(
                                                    "Aplicar Configurações",
                                                    id='apply-palette-button',
                                                    className='config-button',
                                                    style={
                                                        'backgroundColor': '#6200EA',
                                                        'color': 'white',
                                                        'border': 'none',
                                                        'padding': '10px 20px',
                                                        'borderRadius': '4px',
                                                        'cursor': 'pointer',
                                                        'fontSize': '16px',
                                                        'fontWeight': 'bold'
                                                    }
                                                ),
                                                html.Div(
                                                    id='config-save-status',
                                                    style={'marginTop': '10px', 'height': '20px'}
                                                )
                                            ]
                                        )
                                    ]
                                )
                            ]
                        ),
                    ],
                ),
            ],
        ),
        footer,
    ],
)

# Função para criar visualizações de exemplo
def create_example_charts(colors):
    # Dados de exemplo
    categories = ['Categoria A', 'Categoria B', 'Categoria C', 'Categoria D', 'Categoria E']
    values = [25, 38, 15, 22, 10]
    
    # Gráfico de pizza
    pie_chart = go.Figure(data=[go.Pie(
        labels=categories,
        values=values,
        marker=dict(colors=colors[:len(categories)])
    )])
    
    pie_chart.update_layout(
        title='Exemplo de Gráfico de Pizza',
        height=300,
        margin=dict(l=20, r=20, t=40, b=20)
    )
    
    # Gráfico de barras
    bar_chart = go.Figure(data=[go.Bar(
        x=categories,
        y=values,
        marker_color=colors[:len(categories)]
    )])
    
    bar_chart.update_layout(
        title='Exemplo de Gráfico de Barras',
        height=300,
        margin=dict(l=20, r=20, t=40, b=20)
    )
    
    return pie_chart, bar_chart

# Função para salvar as configurações
def save_configuration(selected_palette):
    config_dir = 'config'
    config_file = os.path.join(config_dir, 'app_config.json')
    
    # Criar diretório se não existir
    if not os.path.exists(config_dir):
        os.makedirs(config_dir)
    
    # Carregar configuração existente ou criar nova
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
    else:
        config = {}
    
    # Atualizar a configuração
    config['color_palette'] = selected_palette
    
    # Salvar configuração
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=4)
    
    return True

# Função para carregar as configurações
def load_configuration():
    config_file = 'config/app_config.json'
    
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config.get('color_palette', 'default')
    else:
        return 'default'

# Função para registrar callbacks
def register_callbacks(app):
    # Callback para atualizar a descrição da paleta
    @app.callback(
        Output('palette-description', 'children'),
        [Input('palette-selector', 'value')]
    )
    def update_palette_description(selected_palette):
        return COLOR_PALETTES[selected_palette]['description']
    
    # Callback para atualizar os exemplos de visualização
    @app.callback(
        [Output('palette-example-pie', 'figure'),
         Output('palette-example-bar', 'figure')],
        [Input('palette-selector', 'value')]
    )
    def update_example_charts(selected_palette):
        colors = COLOR_PALETTES[selected_palette]['colors']
        return create_example_charts(colors)
    
    # Callback para atualizar a exibição da paleta atual
    @app.callback(
        [Output('current-palette-name', 'children'),
         Output('current-palette-display', 'children')],
        [Input('palette-selector', 'value')]
    )
    def update_current_palette_display(selected_palette):
        palette_name = COLOR_PALETTES[selected_palette]['name']
        colors = COLOR_PALETTES[selected_palette]['colors']
        
        color_elements = []
        for color in colors:
            color_box = html.Div(
                style={
                    'backgroundColor': color,
                    'width': '30px',
                    'height': '30px',
                    'borderRadius': '4px',
                    'border': '1px solid #ddd'
                }
            )
            color_elements.append(color_box)
        
        return palette_name, color_elements
    
    # Callback para salvar as configurações
    @app.callback(
        Output('config-save-status', 'children'),
        [Input('apply-palette-button', 'n_clicks')],
        [State('palette-selector', 'value')]
    )
    def save_palette_configuration(n_clicks, selected_palette):
        if n_clicks is None:
            return ""
        
        success = save_configuration(selected_palette)
        if success:
            return html.Span("Configurações salvas com sucesso!", style={'color': 'green'})
        else:
            return html.Span("Erro ao salvar configurações.", style={'color': 'red'})
    
    # Callback para carregar configuração ao iniciar
    @app.callback(
        Output('palette-selector', 'value'),
        [Input('configuracoes-content', 'children')],
        prevent_initial_call=False
    )
    def load_palette_configuration(_):
        return load_configuration()