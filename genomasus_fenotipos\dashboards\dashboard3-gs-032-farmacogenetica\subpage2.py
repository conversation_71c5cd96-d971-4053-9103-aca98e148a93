import pandas as pd
from dash import html, dcc, Input, Output
from components.navbar import create_navbar_for_route
from components.dynamic_sidebar import create_sidebar_for_route
from components.footer import footer
import json
import os
from . import DATA_FILE

def get_current_palette():
    # Paletas de cores definidas
    COLOR_PALETTES = {
        "default": ["#6200EA", "#BB86FC", "#03DAC6", "#018786", "#FFEB3B", "#FFC107", "#FF9800", "#F44336", "#E91E63", "#9C27B0", "#673AB7", "#3F51B5"],
        "colorblind": ["#000000", "#E69F00", "#56B4E9", "#009E73", "#F0E442", "#0072B2", "#D55E00", "#CC79A7", "#999999", "#332288", "#44AA99", "#882255"],
        "autistic": ["#7F95D1", "#7FC9D1", "#7FD195", "#D1C97F", "#D17F95", "#957FD1", "#D1957F", "#95D17F", "#7F95D1", "#7FBCD1", "#D17F95", "#957FD1"],
        "monochrome": ["#0D47A1", "#1565C0", "#1976D2", "#1E88E5", "#2196F3", "#42A5F5", "#64B5F6", "#90CAF9", "#BBDEFB", "#E3F2FD", "#2962FF", "#0D47A1"],
        "pastel": ["#FFB6C1", "#FFD700", "#98FB98", "#ADD8E6", "#FFA07A", "#DDA0DD", "#87CEFA", "#F0E68C", "#E6E6FA", "#FFDAB9", "#B0E0E6", "#FFDEAD"]
    }

    config_file = 'config/app_config.json'
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        palette_key = config.get('color_palette', 'default')
    else:
        palette_key = 'default'

    return COLOR_PALETTES.get(palette_key, COLOR_PALETTES['default'])

# Carregamento e processamento dos dados
df = pd.read_csv(DATA_FILE)

# Categorizar idade
def categorize_age(age):
    if pd.isna(age):
        return 'Não informado'
    elif age < 30:
        return '< 30 anos'
    elif age < 40:
        return '30-39 anos'
    elif age < 50:
        return '40-49 anos'
    elif age < 60:
        return '50-59 anos'
    elif age < 70:
        return '60-69 anos'
    elif age < 80:
        return '70-79 anos'
    else:
        return '≥ 80 anos'

df['idade_categoria'] = df['age_years'].apply(categorize_age)

def create_graph(title, figure, palette_colors=None):
    if palette_colors is None:
        palette_colors = get_current_palette()

    if 'data' in figure and figure['data']:
        for i, trace in enumerate(figure['data']):
            if 'marker' in trace:
                if 'color' in trace['marker'] and isinstance(trace['marker']['color'], str):
                    color_index = i % len(palette_colors)
                    trace['marker']['color'] = palette_colors[color_index]
                elif 'colors' in trace['marker'] and isinstance(trace['marker']['colors'], list):
                    num_colors = len(trace['marker']['colors'])
                    trace['marker']['colors'] = palette_colors[:num_colors]

    return html.Div([
        html.H3(title, style={'textAlign': 'center'}),
        dcc.Graph(figure=figure)
    ], className='graph-container')

# Layout da subpágina de Diabetes
content = html.Div([
    html.H1("Estatísticas Diabetes - Farmacogenética (GS-032)", style={'textAlign': 'center'}),



    # Filtros globais
    html.Div([
        html.Label("Filtrar por Sexo:", style={'marginRight': '10px'}),
        dcc.Dropdown(
            id='sexo-filter-subpage2-dashboard3',
            options=[{'label': 'Todos', 'value': 'all'}] +
                    [{'label': 'Feminino', 'value': 1}, {'label': 'Masculino', 'value': 0}],
            value='all',
            clearable=False,
            style={'width': '200px'}
        ),
        html.Label("Filtrar por Raça/Cor:", style={'marginLeft': '20px', 'marginRight': '10px'}),
        dcc.Dropdown(
            id='raca-filter-subpage2-dashboard3',
            options=[{'label': 'Todos', 'value': 'all'}] +
                    [{'label': 'Branco', 'value': 'ethnicity_race_white'},
                     {'label': 'Pardo', 'value': 'ethnicity_race_pardo'},
                     {'label': 'Preto', 'value': 'ethnicity_race_black'},
                     {'label': 'Amarelo', 'value': 'ethnicity_race_yellow'},
                     {'label': 'Indígena', 'value': 'ethnicity_race_indigenous'}],
            value='all',
            clearable=False,
            style={'width': '200px'}
        )
    ], style={'display': 'flex', 'justifyContent': 'center', 'marginBottom': '20px'}),

    # Gráficos base (serão atualizados dinamicamente)
    html.Div(id='dynamic-graphs-subpage2-dashboard3')
], className='content')

# Layout principal com a estrutura comum
layout = html.Div(
    className='page-container',
    children=[
        create_navbar_for_route("/dashboard3/farmacogenetica-diabetes"),
        html.Div([
            create_sidebar_for_route("/dashboard3/farmacogenetica-diabetes"),
            content,
        ], className='main-section', style={'display': 'flex'}),
        footer,
    ],
)

# Callbacks
def register_callbacks(app):
    @app.callback(
        Output('dynamic-graphs-subpage2-dashboard3', 'children'),
        [Input('sexo-filter-subpage2-dashboard3', 'value'), Input('raca-filter-subpage2-dashboard3', 'value')]
    )
    def update_graphs(sexo_filter, raca_filter):
        palette_colors = get_current_palette()

        filtered_df = df.copy()
        if sexo_filter != 'all':
            filtered_df = filtered_df[filtered_df['sex_feminino'] == sexo_filter]

        # Aplicar filtro de raça/cor
        if raca_filter != 'all':
            filtered_df = filtered_df[filtered_df[raca_filter] == 1]

        # Pergunta 18: Prevalência da diabetes no estudo
        diabetes_columns = ['diabetes_type_no_diabetes', 'diabetes_type_type_1', 'diabetes_type_type_2', 'diabetes_type_missing']
        diabetes_labels = ['Sem diabetes', 'Diabetes Tipo 1', 'Diabetes Tipo 2', 'Não informado']
        diabetes_values = []

        for col in diabetes_columns:
            if col in filtered_df.columns:
                count = filtered_df[col].sum()
                diabetes_values.append(count)
            else:
                diabetes_values.append(0)

        graph_1 = create_graph("Prevalência da Diabetes no Estudo", {
            'data': [{
                'labels': diabetes_labels,
                'values': diabetes_values,
                'type': 'pie',
                'marker': {'colors': palette_colors[:len(diabetes_labels)]}
            }],
            'layout': {'title': 'Distribuição dos Tipos de Diabetes'}
        }, palette_colors)

        # Pergunta 19: Prevalência da diabetes por sexo
        sexo_diabetes_data = []
        sexos = ['Masculino', 'Feminino']
        sexo_values = [0, 1]

        # Para cada tipo de diabetes
        diabetes_types = {
            'diabetes_type_type_1': 'Diabetes Tipo 1',
            'diabetes_type_type_2': 'Diabetes Tipo 2'
        }

        for i, (col, type_name) in enumerate(diabetes_types.items()):
            if col in filtered_df.columns:
                for j, (sexo, value) in enumerate(zip(sexos, sexo_values)):
                    subset = filtered_df[filtered_df['sex_feminino'] == value]
                    total = len(subset)
                    diabeticos = subset[col].sum()
                    prevalencia = (diabeticos / total * 100) if total > 0 else 0

                    sexo_diabetes_data.append({
                        'x': [f'{type_name} - {sexo}'],
                        'y': [prevalencia],
                        'type': 'bar',
                        'name': f'{type_name} - {sexo}',
                        'marker': {'color': palette_colors[(i*2 + j) % len(palette_colors)]},
                        'text': [f'{diabeticos}/{total}'],
                        'textposition': 'auto'
                    })

        graph_2 = create_graph("Prevalência da Diabetes por Sexo", {
            'data': sexo_diabetes_data,
            'layout': {
                'title': 'Prevalência da Diabetes por Tipo e Sexo',
                'xaxis': {'title': 'Tipo de Diabetes e Sexo'},
                'yaxis': {'title': 'Prevalência (%)', 'tickformat': '.1f', 'ticksuffix': '%'},
                'showlegend': False,
                'xaxis_tickangle': -45
            }
        }, palette_colors)

        # Pergunta 20: Prevalência da diabetes por raça/cor
        raca_columns = {
            'ethnicity_race_white': 'Branco',
            'ethnicity_race_pardo': 'Pardo',
            'ethnicity_race_black': 'Preto',
            'ethnicity_race_yellow': 'Amarelo',
            'ethnicity_race_indigenous': 'Indígena'
        }

        raca_diabetes_data = []
        for i, (col, label) in enumerate(raca_columns.items()):
            if col in filtered_df.columns:
                subset = filtered_df[filtered_df[col] == 1]
                total = len(subset)
                if total > 0:
                    # Contar diabetes tipo 1 e tipo 2
                    tipo1 = subset['diabetes_type_type_1'].sum() if 'diabetes_type_type_1' in subset.columns else 0
                    tipo2 = subset['diabetes_type_type_2'].sum() if 'diabetes_type_type_2' in subset.columns else 0
                    total_diabetes = tipo1 + tipo2
                    prevalencia = (total_diabetes / total * 100)

                    raca_diabetes_data.append({
                        'x': [label],
                        'y': [prevalencia],
                        'type': 'bar',
                        'name': label,
                        'marker': {'color': palette_colors[i % len(palette_colors)]},
                        'text': [f'{total_diabetes}/{total}'],
                        'textposition': 'auto'
                    })

        graph_3 = create_graph("Prevalência da Diabetes por Raça/Cor", {
            'data': raca_diabetes_data,
            'layout': {
                'title': 'Prevalência da Diabetes por Raça/Cor',
                'xaxis': {'title': 'Raça/Cor'},
                'yaxis': {'title': 'Prevalência (%)', 'tickformat': '.1f', 'ticksuffix': '%'},
                'showlegend': False,
                'annotations': [
                    {
                        'text': 'Sem registros',
                        'x': 'Indígena',
                        'y': 5,
                        'showarrow': False,
                        'font': {'size': 12, 'color': 'gray'},
                        'xanchor': 'center'
                    }
                ]
            }
        }, palette_colors)

        # Pergunta 22: Pessoas com diabetes que tomam medicamento
        diabetes_med_data = []

        # Filtrar apenas pessoas com diabetes (tipo 1 ou tipo 2)
        diabetes_subset = filtered_df[
            (filtered_df.get('diabetes_type_type_1', 0) == 1) |
            (filtered_df.get('diabetes_type_type_2', 0) == 1)
        ]

        if len(diabetes_subset) > 0:
            # Contar quem usa medicação regular
            usa_med = diabetes_subset['uses_regular_medication'].sum()
            nao_usa_med = len(diabetes_subset) - usa_med

            diabetes_med_data = [{
                'labels': ['Usa medicação regular', 'Não usa medicação regular'],
                'values': [usa_med, nao_usa_med],
                'type': 'pie',
                'marker': {'colors': [palette_colors[2], palette_colors[3]]}
            }]

        graph_4 = create_graph("Pessoas com Diabetes que Tomam Medicamento", {
            'data': diabetes_med_data,
            'layout': {'title': 'Uso de Medicação entre Diabéticos'}
        }, palette_colors)

        # Análise de medicamentos específicos para diabetes
        diabetes_medications = []
        if len(diabetes_subset) > 0 and 'medication_details' in diabetes_subset.columns:
            # Medicamentos comuns para diabetes
            diabetes_drugs = ['METFORMINA', 'GLIFAGE', 'INSULINA', 'GLICAZIDA', 'GLICLAZIDA', 'DAPAGLIFLOZINA', 'FORXIGA']

            med_counts = {}
            for drug in diabetes_drugs:
                count = diabetes_subset['medication_details'].str.upper().str.contains(drug, na=False).sum()
                if count > 0:
                    med_counts[drug] = count

            # Correção da duplicata GLICAZIDA/GLICLAZIDA
            if 'GLICAZIDA' in med_counts and 'GLICLAZIDA' in med_counts:
                # Unificar sob o nome correto GLICLAZIDA
                total_gliclazida = med_counts['GLICAZIDA'] + med_counts['GLICLAZIDA']
                med_counts['GLICLAZIDA'] = total_gliclazida
                del med_counts['GLICAZIDA']  # Remover a entrada incorreta
            elif 'GLICAZIDA' in med_counts:
                # Renomear GLICAZIDA para GLICLAZIDA (nome correto)
                med_counts['GLICLAZIDA'] = med_counts['GLICAZIDA']
                del med_counts['GLICAZIDA']

            if med_counts:
                # Ordenar medicamentos por frequência (decrescente)
                sorted_meds = sorted(med_counts.items(), key=lambda x: x[1], reverse=True)

                diabetes_medications = [{
                    'x': [med[0] for med in sorted_meds],
                    'y': [med[1] for med in sorted_meds],
                    'type': 'bar',
                    'marker': {'color': palette_colors[4]}
                }]

        graph_5 = create_graph("Medicamentos Mais Usados por Diabéticos", {
            'data': diabetes_medications,
            'layout': {
                'title': 'Medicamentos Antidiabéticos Mais Comuns',
                'xaxis': {'title': 'Medicamento'},
                'yaxis': {'title': 'Número de pacientes'},
                'xaxis_tickangle': -45
            }
        }, palette_colors)

        # Heatmap: Diabetes por sexo e raça/cor
        heatmap_data = []
        x_labels = []
        y_labels = ['Masculino', 'Feminino']
        z_values = []

        # Preparar dados para o heatmap
        for sexo_val, sexo_label in zip([0, 1], y_labels):
            row = []
            for col, raca_label in raca_columns.items():
                if col in filtered_df.columns:
                    subset = filtered_df[(filtered_df['sex_feminino'] == sexo_val) & (filtered_df[col] == 1)]
                    total = len(subset)
                    if total > 0:
                        # Contar diabetes (tipo 1 + tipo 2)
                        tipo1 = subset.get('diabetes_type_type_1', pd.Series([0] * len(subset))).sum()
                        tipo2 = subset.get('diabetes_type_type_2', pd.Series([0] * len(subset))).sum()
                        total_diabetes = tipo1 + tipo2
                        prevalencia = (total_diabetes / total * 100)
                    else:
                        prevalencia = 0
                    row.append(prevalencia)
                    if sexo_val == 0:  # Adicionar labels apenas uma vez
                        x_labels.append(raca_label)
            z_values.append(row)

        if x_labels and z_values:
            heatmap_data = [{
                'z': z_values,
                'x': x_labels,
                'y': y_labels,
                'type': 'heatmap',
                'colorscale': 'Blues',
                'showscale': True,
                'colorbar': {'title': 'Prevalência (%)'},
                'hovertemplate': 'Sexo: %{y}<br>Raça/Cor: %{x}<br>Prevalência: %{z:.1f}%<extra></extra>'
            }]

        graph_6 = create_graph("Prevalência de Diabetes por Sexo e Raça/Cor", {
            'data': heatmap_data,
            'layout': {
                'title': 'Heatmap: Prevalência de Diabetes por Sexo e Raça/Cor',
                'xaxis': {'title': 'Raça/Cor'},
                'yaxis': {'title': 'Sexo'},
                'height': 400
            }
        }, palette_colors)

        # Comparação entre tipos de diabetes
        tipos_comparison = []
        if 'diabetes_type_type_1' in filtered_df.columns and 'diabetes_type_type_2' in filtered_df.columns:
            tipo1_count = filtered_df['diabetes_type_type_1'].sum()
            tipo2_count = filtered_df['diabetes_type_type_2'].sum()

            tipos_comparison = [{
                'x': ['Diabetes Tipo 1', 'Diabetes Tipo 2'],
                'y': [tipo1_count, tipo2_count],
                'type': 'bar',
                'marker': {'color': [palette_colors[0], palette_colors[1]]},
                'text': [tipo1_count, tipo2_count],
                'textposition': 'auto'
            }]

        graph_7 = create_graph("Comparação entre Tipos de Diabetes", {
            'data': tipos_comparison,
            'layout': {
                'title': 'Número de Casos por Tipo de Diabetes',
                'xaxis': {'title': 'Tipo de Diabetes'},
                'yaxis': {'title': 'Número de casos'},
                'showlegend': False
            }
        }, palette_colors)

        return html.Div([
            html.Div([graph_1, graph_2], className='row'),
            html.Div([graph_3, graph_4], className='row'),
            html.Div([graph_5, graph_6], className='row'),
            html.Div([graph_7], className='row')
        ])