import pandas as pd
from dash import html, dcc, Dash, Input, Output, State
from components.navbar import create_navbar_for_route
from components.dynamic_sidebar import create_sidebar_for_route
from components.footer import footer
import json
import os

def get_current_palette():
    COLOR_PALETTES = {
        "default": ["#6200EA", "#BB86FC", "#03DAC6", "#018786", "#FFEB3B", "#FFC107", "#FF9800", "#F44336", "#E91E63", "#9C27B0", "#673AB7", "#3F51B5"],
        "colorblind": ["#000000", "#E69F00", "#56B4E9", "#009E73", "#F0E442", "#0072B2", "#D55E00", "#CC79A7", "#999999", "#332288", "#44AA99", "#882255"],
        "autistic": ["#7F95D1", "#7FC9D1", "#7FD195", "#D1C97F", "#D17F95", "#957FD1", "#D1957F", "#95D17F", "#7F95D1", "#7FBCD1", "#D17F95", "#957FD1"],
        "monochrome": ["#0D47A1", "#1565C0", "#1976D2", "#1E88E5", "#2196F3", "#42A5F5", "#64B5F6", "#90CAF9", "#BBDEFB", "#E3F2FD", "#2962FF", "#0D47A1"],
        "pastel": ["#FFB6C1", "#FFD700", "#98FB98", "#ADD8E6", "#FFA07A", "#DDA0DD", "#87CEFA", "#F0E68C", "#E6E6FA", "#FFDAB9", "#B0E0E6", "#FFDEAD"]
    }
    
    config_file = 'config/app_config.json'
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        palette_key = config.get('color_palette', 'default')
    else:
        palette_key = 'default'
    
    return COLOR_PALETTES.get(palette_key, COLOR_PALETTES['default'])

# Layout da página
layout = html.Div(
    className='page-container',
    children=[
        html.Link(
            rel='stylesheet',
            href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
        ),
        
        create_navbar_for_route("/dashboard3/farmacogenetica-sobre-o-estudo"),
        html.Div(
            className='main-wrapper',
            id='main-wrapper',
            children=[
                create_sidebar_for_route("/dashboard3/farmacogenetica-sobre-o-estudo"),
                html.Div(
                    className='content',
                    children=[
                        html.Div(
                            style={
                                'padding': '30px',
                                'max-width': '1000px',
                                'margin': '0 auto',
                                'background-color': '#ffffff',
                                'border-radius': '8px',
                                'box-shadow': '0 2px 10px rgba(0,0,0,0.1)',
                                'margin-top': '20px'
                            },
                            children=[
                                # Título da página
                                html.H1(
                                    children=[
                                        html.Div("Sobre o Estudo GS-032 | EPIGEN-II-PGx-cardiovascular", style={'font-weight': '600', 'color': '#6200EA'}),
                                        html.Div("Farmacogenética", style={'font-size': '0.7em', 'font-weight': '400', 'color': '#666', 'margin-top': '5px'})
                                    ],
                                    style={
                                        'text-align': 'center',
                                        'margin-bottom': '40px',
                                        'padding-bottom': '20px',
                                        'border-bottom': '2px solid #6200EA'
                                    }
                                ),
                                
                                # Descrição do estudo
                                html.P(
                                    "O estudo GS-032 EPIGEN-II-PGx-cardiovascular é uma pesquisa de farmacogenética focada em doenças cardiovasculares, desenvolvida no contexto do projeto GenomaSUS. Este estudo investiga como variações genéticas influenciam a resposta a medicamentos cardiovasculares, especialmente o clopidogrel, um antiagregante plaquetário amplamente utilizado em pacientes com doenças coronarianas. A pesquisa analisa 192 participantes com perfil cardiovascular específico, incluindo pacientes que realizaram procedimentos de angioplastia tanto eletiva quanto de emergência. O objetivo principal é compreender como fatores genéticos, demográficos e clínicos interagem para determinar a eficácia e segurança dos tratamentos farmacológicos em populações brasileiras diversas.",
                                    style={
                                        'text-align': 'justify',
                                        'line-height': '1.6',
                                        'font-size': '16px',
                                        'color': '#333',
                                        'margin-bottom': '30px'
                                    }
                                ),

                                # Botão de volta
                                html.Div(
                                    style={'text-align': 'center', 'margin-top': '40px'},
                                    children=[
                                        dcc.Link(
                                            html.Button(
                                                "Voltar aos Dashboards",
                                                style={
                                                    'background-color': '#6200EA',
                                                    'color': '#ffffff',
                                                    'padding': '12px 24px',
                                                    'border': 'none',
                                                    'border-radius': '6px',
                                                    'font-size': '16px',
                                                    'font-weight': '500',
                                                    'cursor': 'pointer',
                                                    'transition': 'all 0.3s ease',
                                                    'box-shadow': '0 2px 4px rgba(98, 0, 234, 0.2)',
                                                }
                                            ),
                                            href='/dashboard3/farmacogenetica-sociodemografico',
                                            style={'text-decoration': 'none'}
                                        )
                                    ]
                                )
                            ]
                        )
                    ],
                ),
            ],
        ),
        footer,
    ],
)

def register_callbacks(app):
    pass
