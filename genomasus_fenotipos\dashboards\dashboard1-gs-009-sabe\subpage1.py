import pandas as pd
from dash import html, dcc, Input, Output
from components.navbar import create_navbar_for_route
from components.dynamic_sidebar import create_sidebar_for_route
from components.footer import footer
import plotly.express as px
import json
import os
from . import DATA_FILE

def get_current_palette():
    # Paletas de cores definidas
    COLOR_PALETTES = {
        "default": ["#6200EA", "#BB86FC", "#03DAC6", "#018786", "#FFEB3B", "#FFC107", "#FF9800", "#F44336", "#E91E63", "#9C27B0", "#673AB7", "#3F51B5"],
        "colorblind": ["#000000", "#E69F00", "#56B4E9", "#009E73", "#F0E442", "#0072B2", "#D55E00", "#CC79A7", "#999999", "#332288", "#44AA99", "#882255"],
        "autistic": ["#7F95D1", "#7FC9D1", "#7FD195", "#D1C97F", "#D17F95", "#957FD1", "#D1957F", "#95D17F", "#7F95D1", "#7FBCD1", "#D17F95", "#957FD1"],
        "monochrome": ["#0D47A1", "#1565C0", "#1976D2", "#1E88E5", "#2196F3", "#42A5F5", "#64B5F6", "#90CAF9", "#BBDEFB", "#E3F2FD", "#2962FF", "#0D47A1"],
        "pastel": ["#FFB6C1", "#FFD700", "#98FB98", "#ADD8E6", "#FFA07A", "#DDA0DD", "#87CEFA", "#F0E68C", "#E6E6FA", "#FFDAB9", "#B0E0E6", "#FFDEAD"]
    }
    
    # Verifica se existe arquivo de configuração
    config_file = 'config/app_config.json'
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        palette_key = config.get('color_palette', 'default')
    else:
        palette_key = 'default'
    
    return COLOR_PALETTES.get(palette_key, COLOR_PALETTES['default'])

df = pd.read_csv(DATA_FILE)

df['idade'] = 2010 - df['Ano1aa_nasc']
df = df[df['idade'].between(0, 110)]

def create_graph(title, figure, palette_colors=None):
    if palette_colors is None:
        palette_colors = get_current_palette()
    
    if 'data' in figure and figure['data']:
        for i, trace in enumerate(figure['data']):
            if 'marker' in trace:
                if 'color' in trace['marker'] and isinstance(trace['marker']['color'], str):
                    color_index = i % len(palette_colors)
                    trace['marker']['color'] = palette_colors[color_index]
                elif 'colors' in trace['marker'] and isinstance(trace['marker']['colors'], list):
                    num_colors = len(trace['marker']['colors'])
                    trace['marker']['colors'] = palette_colors[:num_colors]
    
    return html.Div([
        html.H3(title, style={'textAlign': 'center'}),
        dcc.Graph(figure=figure)
    ], className='graph-container')

# Layout da subpágina de Hipertensão
content = html.Div([
    html.H1("Estatísticas Hipertensão - SABE (GS-009)", style={'textAlign': 'center'}),
    
    # Filtros globais
    html.Div([
        html.Label("Filtrar por Sexo:", style={'marginRight': '10px'}),
        dcc.Dropdown(
            id='sexo-filter-subpage1',
            options=[{'label': 'Todos', 'value': 'all'}] + 
                    [{'label': 'Masculino' if x == False else 'Feminino', 'value': x} for x in [False, True]],
            value='all',
            clearable=False,
            style={'width': '200px'}
        ),
        html.Label("Filtrar por Raça/Cor:", style={'marginLeft': '20px', 'marginRight': '10px'}),
        dcc.Dropdown(
            id='raca-filter-subpage1',
            options=[{'label': 'Todos', 'value': 'all'}] + 
                    [{'label': col.replace('A12_raca_', '').capitalize(), 'value': col} for col in df.columns if 'A12_raca_' in col],
            value='all',
            clearable=False,
            style={'width': '200px'}
        )
    ], style={'display': 'flex', 'justifyContent': 'center', 'marginBottom': '20px'}),
    
    # Gráficos base (serão atualizados dinamicamente)
    html.Div(id='dynamic-graphs-subpage1')
], className='content')

# Layout principal com a estrutura comum
layout = html.Div(
    className='page-container',
    children=[
        create_navbar_for_route("/dashboard1/sabe-hipertensao"),
        html.Div([
            create_sidebar_for_route("/dashboard1/sabe-hipertensao"),
            content,  
        ], className='main-section', style={'display': 'flex'}),
        footer,  
    ],
)

# Callbacks
def register_callbacks(app):
    @app.callback(
        Output('dynamic-graphs-subpage1', 'children'),
        [Input('sexo-filter-subpage1', 'value'), Input('raca-filter-subpage1', 'value')]
    )
    def update_graphs(sexo_filter, raca_filter):
        palette_colors = get_current_palette()
        
        filtered_df = df.copy()
        if sexo_filter != 'all':
            filtered_df = filtered_df[filtered_df['C18_entrevistado_sex_fem'] == sexo_filter]
        
        # Aplicar filtro de raça/cor
        if raca_filter != 'all':
            filtered_df = filtered_df[filtered_df[raca_filter] == 1]

        graph_13 = create_graph("Prevalência da Hipertensão", {
            'data': [{
                'labels': ['Hipertenso', 'Não Hipertenso'],
                'values': filtered_df['C04_tem_hipertensao'].value_counts(),
                'type': 'pie',
                'marker': {'colors': [palette_colors[0], palette_colors[1]]}
            }],
            'layout': {'title': 'Prevalência da Hipertensão'}
        }, palette_colors)

        graph_14 = create_graph("Prevalência da Hipertensão por Idade e Sexo", {
            'data': [
                {
                    'x': filtered_df[(filtered_df['C18_entrevistado_sex_fem'] == False) & (filtered_df['C04_tem_hipertensao'] == True)]['idade'],
                    'name': 'Masculino Hipertenso',
                    'type': 'histogram',
                    'marker': {'color': '#4472C4'},  # Azul sólido
                    'nbinsx': 15
                },
                {
                    'x': filtered_df[(filtered_df['C18_entrevistado_sex_fem'] == True) & (filtered_df['C04_tem_hipertensao'] == True)]['idade'],
                    'name': 'Feminino Hipertenso',
                    'type': 'histogram',
                    'marker': {'color': '#E15759'},  # Vermelho sólido
                    'nbinsx': 15
                }
            ],
            'layout': {
                'barmode': 'group',
                'title': 'Prevalência da Hipertensão por Idade e Sexo',
                'xaxis': {'title': 'Idade (anos)'},
                'yaxis': {'title': 'Número de hipertensos'},
                'legend': {'orientation': 'h', 'y': -0.15},
                'bargap': 0.1
            }
        }, palette_colors)

        raca_prevalencia_rank = []
        for col_raca in [c for c in filtered_df.columns if 'A12_raca_' in c]:
            total_pessoas = filtered_df[filtered_df[col_raca] == 1].shape[0]
            hipertensos = filtered_df[(filtered_df[col_raca] == 1) & (filtered_df['C04_tem_hipertensao'] == True)].shape[0]
            
            if total_pessoas > 0:
                prevalencia = (hipertensos / total_pessoas) * 100
                categoria = col_raca.replace("A12_raca_", "").capitalize()
                raca_prevalencia_rank.append({'categoria': categoria, 'prevalencia': prevalencia})

        # Ordenar o rank pela prevalência (do maior para o menor)
        raca_prevalencia_rank = sorted(raca_prevalencia_rank, key=lambda x: x['prevalencia'], reverse=True)

        raca_data = []
        for i, rank in enumerate(raca_prevalencia_rank):
            color_index = i % len(palette_colors)  
            raca_data.append({
                'x': [rank['categoria']],
                'y': [rank['prevalencia']],
                'type': 'bar',
                'name': rank['categoria'],
                'marker': {'color': palette_colors[color_index]},
                'opacity': 0.8,
            })

        graph_15_1 = create_graph("Rank de Prevalência da Hipertensão por Raça/Cor", {
            'data': raca_data,
            'layout': {
                'barmode': 'group',
                'title': 'Rank de Prevalência da Hipertensão por Raça/Cor',
                'xaxis': {'title': 'Raça/Cor'},  
                'yaxis': {'title': 'Prevalência (%)', 'tickformat': '.1f', 'ticksuffix': '%'}, 
                'bargap': 0.2, 
                'bargroupgap': 0.1, 
                'legend': {'orientation': 'h', 'yanchor': 'bottom', 'y': 1.02, 'xanchor': 'right', 'x': 1},  
            }
        }, palette_colors)

        escolaridade_prevalencia_rank = []
        for col_escola in [c for c in filtered_df.columns if 'A06n_edu_' in c]:
            total_pessoas = filtered_df[filtered_df[col_escola] == 1].shape[0]
            hipertensos = filtered_df[(filtered_df[col_escola] == 1) & (filtered_df['C04_tem_hipertensao'] == True)].shape[0]

            if total_pessoas > 0:
                prevalencia = (hipertensos / total_pessoas) * 100
                categoria = col_escola.replace("A06n_edu_", "").capitalize()
                escolaridade_prevalencia_rank.append({'categoria': categoria, 'prevalencia': prevalencia})

        escolaridade_prevalencia_rank = sorted(escolaridade_prevalencia_rank, key=lambda x: x['prevalencia'], reverse=True)

        escolaridade_data = []
        for i, rank in enumerate(escolaridade_prevalencia_rank):
            color_index = i % len(palette_colors)
            escolaridade_data.append({
                'x': [rank['categoria']],
                'y': [rank['prevalencia']],
                'type': 'bar',
                'name': rank['categoria'],
                'marker': {'color': palette_colors[color_index]},
                'opacity': 0.8,
            })

        graph_15_2 = create_graph("Rank de Prevalência da Hipertensão por Escolaridade", {
            'data': escolaridade_data,
            'layout': {
                'barmode': 'group',
                'title': 'Rank de Prevalência da Hipertensão por Escolaridade',
                'xaxis': {'title': 'Escolaridade'}, 
                'yaxis': {'title': 'Prevalência (%)', 'tickformat': '.1f', 'ticksuffix': '%'},  
                'bargap': 0.2, 
                'bargroupgap': 0.1,  
                'legend': {'orientation': 'h', 'yanchor': 'bottom', 'y': 1.02, 'xanchor': 'right', 'x': 1}, 
            }
        }, palette_colors)

        graph_16 = create_graph("Distribuição de Idades com Diagnóstico de Hipertensão", {
            'data': [{'x': filtered_df[filtered_df['C04_tem_hipertensao'] == True]['idade'], 'type': 'histogram', 'marker': {'color': palette_colors[3]}}],
            'layout': {'title': 'Distribuição de Idades com Diagnóstico de Hipertensão'}
        }, palette_colors)

        graph_17 = create_graph("Pessoas com Hipertensão que Tomam Medicamento", {
            'data': [
                {'x': filtered_df[(filtered_df['C04_tem_hipertensao'] == True) & (filtered_df['C04g1_hiper_contr_1_trat_medicamento'] == True)]['idade'], 'name': 'Tomam Medicamento', 'type': 'histogram', 'marker': {'color': palette_colors[0]}},
                {'x': filtered_df[(filtered_df['C04_tem_hipertensao'] == True) & (filtered_df['C04g1_hiper_contr_1_trat_medicamento'] == False)]['idade'], 'name': 'Não Tomam Medicamento', 'type': 'histogram', 'marker': {'color': palette_colors[1]}}
            ],
            'layout': {'barmode': 'stack', 'title': 'Pessoas com Hipertensão que Tomam Medicamento'}
        }, palette_colors)

        return html.Div([
            html.Div([graph_13, graph_14], className='row'),
            html.Div([graph_15_1, graph_15_2], className='row'),
            html.Div([graph_16, graph_17], className='row')
        ])