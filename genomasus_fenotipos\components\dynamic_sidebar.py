from dash import html, dcc

# Configurações de menu por dashboard
DASHBOARD_MENUS = {
    "dashboard1": [
        {'href': '/dashboard1/sabe-sociodemografico', 'icon': 'fa fa-home', 'text': ' Sociodemográfico'},
        {'href': '/dashboard1/sabe-hipertensao', 'icon': 'fa fa-chart-line', 'text': ' Hipertensão'},
        {'href': '/dashboard1/sabe-diabetes', 'icon': 'fa fa-table', 'text': ' Diabe<PERSON>'},
        {'href': '/dashboard1/sabe-sobre-o-estudo', 'icon': 'fa fa-info-circle', 'text': ' Sobre o Estudo'},
        {'href': '/dashboard1/renomica-dimensoes-estudo-sabe', 'icon': 'fa fa-clipboard-check', 'text': ' Dimensões de Qualidade do Estudo'}
    ],
    "dashboard2": [
        {'href': '/dashboard2/renomica-sociodemografico', 'icon': 'fa fa-users', 'text': ' Sociodemográfico'},
        {'href': '/dashboard2/renomica-hipertensao', 'icon': 'fa fa-heartbeat', 'text': ' Hipertensão'},
        {'href': '/dashboard2/renomica-diabetes', 'icon': 'fa fa-tint', 'text': ' Diabetes'},
        {'href': '/dashboard2/renomica-tabagismo-alcoolismo', 'icon': 'fa fa-smoking', 'text': ' Tabagismo e Alcoolismo'},
        {'href': '/dashboard2/renomica-doencas-cardiacas', 'icon': 'fa fa-heart', 'text': ' Doenças Cardíacas'},
        {'href': '/dashboard2/renomica-sobre-o-estudo', 'icon': 'fa fa-info-circle', 'text': ' Sobre o Estudo'},
        {'href': '/dashboard2/renomica-dimensoes-estudo-renomicadac', 'icon': 'fa fa-clipboard-check', 'text': ' Dimensões de Qualidade do Estudo'}
    ],
    "dashboard3": [
        {'href': '/dashboard3/farmacogenetica-sociodemografico', 'icon': 'fa fa-users', 'text': ' Sociodemográfico'},
        {'href': '/dashboard3/farmacogenetica-hipertensao', 'icon': 'fa fa-heartbeat', 'text': ' Hipertensão'},
        {'href': '/dashboard3/farmacogenetica-diabetes', 'icon': 'fa fa-tint', 'text': ' Diabetes'},
        {'href': '/dashboard3/farmacogenetica-tabagismo-alcoolismo', 'icon': 'fa fa-smoking', 'text': ' Tabagismo e Alcoolismo'},
        {'href': '/dashboard3/farmacogenetica-clopidogrel-doencas-cardiacas', 'icon': 'fa fa-heart', 'text': ' Clopidogrel e Doenças Cardíacas'},
        {'href': '/dashboard3/farmacogenetica-medicamentos-comorbidades', 'icon': 'fa fa-pills', 'text': ' Medicamentos e Comorbidades'},
        {'href': '/dashboard3/farmacogenetica-sobre-o-estudo', 'icon': 'fa fa-info-circle', 'text': ' Sobre o Estudo'},
        {'href': '/dashboard3/farmacogenetica-dimensoes-estudo', 'icon': 'fa fa-clipboard-check', 'text': ' Dimensões de Qualidade do Estudo'}
    ]
}

def _create_menu_items(menu_items, current_route):
    menu_children = []

    for item in menu_items:
        is_active = current_route == item['href'] if current_route else False
        link_class = 'active' if is_active else ''

        menu_children.append(
            html.Li(
                dcc.Link(
                    href=item['href'],
                    className=link_class,
                    children=[
                        html.I(className=item['icon'], style={"margin-right": "12px"}),
                        item['text']
                    ]
                )
            )
        )

    return menu_children

def create_sidebar(dashboard_type="dashboard1", current_route=None):
    # Obter itens do menu
    menu_items = DASHBOARD_MENUS.get(dashboard_type, [])
    menu_children = _create_menu_items(menu_items, current_route)
    return html.Div(
        className='sidebar',
        id='sidebar',
        children=[
            html.H2("Menu"),
            html.Ul(
                className='sidebar-menu',
                children=menu_children
            ),
        ],
    )

def create_sidebar_for_route(route):
    # Determinar tipo de dashboard baseado na rota
    if route.startswith('/dashboard1'):
        dashboard_type = "dashboard1"
    elif route.startswith('/dashboard2'):
        dashboard_type = "dashboard2"
    elif route.startswith('/dashboard3'):
        dashboard_type = "dashboard3"
    else:
        dashboard_type = "dashboard1"  # padrão

    return create_sidebar(dashboard_type, current_route=route)

# Sidebar padrão para compatibilidade
sidebar = create_sidebar("dashboard1")
