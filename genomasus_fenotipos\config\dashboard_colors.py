#!/usr/bin/env python3
"""
Configuração centralizada de cores para dashboards
Sincroniza cores entre cards da página inicial e navbars dos dashboards
"""

# Mapeamento de cores por dashboard
DASHBOARD_COLORS = {
    # Dashboard 1 - SABE
    "dashboard1": {
        "color": "#ADD8E6",
        "name": "GS-009 SABE",
        "routes": [
            "/dashboard1/sabe-sociodemografico",
            "/dashboard1/sabe-hipertensao",
            "/dashboard1/sabe-diabetes",
            "/dashboard1/sabe-sobre-o-estudo",
            "/dashboard1/renomica-dimensoes-estudo-sabe"
        ]
    },

    # Dashboard 2 - Renomica-DAC
    "dashboard2": {
        "color": "#b7d5ac",
        "name": "GS-004 Renomica-DAC",
        "routes": [
            "/dashboard2/renomica-sociodemografico",
            "/dashboard2/renomica-hipertensao",
            "/dashboard2/renomica-diabetes",
            "/dashboard2/renomica-tabagismo-alcoolismo",
            "/dashboard2/renomica-doencas-cardiacas",
            "/dashboard2/renomica-sobre-o-estudo",
            "/dashboard2/renomica-dimensoes-estudo-renomicadac"
        ]
    },
    
    # Dashboard 3 - Farmacogenética
    "dashboard3": {
        "color": "#DDA0DD",
        "name": "GS-032 Farmacogenética",
        "routes": [
            "/dashboard3/farmacogenetica-sociodemografico",
            "/dashboard3/farmacogenetica-hipertensao",
            "/dashboard3/farmacogenetica-diabetes",
            "/dashboard3/farmacogenetica-tabagismo-alcoolismo",
            "/dashboard3/farmacogenetica-clopidogrel-doencas-cardiacas",
            "/dashboard3/farmacogenetica-medicamentos-comorbidades",
            "/dashboard3/farmacogenetica-sobre-o-estudo",
            "/dashboard3/farmacogenetica-dimensoes-estudo"
        ]
    },

    # Harmonização de Variáveis
    "harmonizacao": {
        "color": "#F9E79F",
        "name": "Harmonização de Variáveis",
        "routes": [
            "/harmonizacao/comparativo",
            "/harmonizacao/sobre"
        ]
    },

    # Página de Contato
    "contato": {
        "color": "#9ec0f7",
        "name": "Suporte e Contato",
        "routes": [
            "/sistema/contato"
        ]
    }
}

# Cor padrão para páginas que não são dashboards (cinza claro)
DEFAULT_COLOR = "#adb5bd"

def get_dashboard_color(route_or_dashboard_type):
    """
    Retorna a cor do dashboard baseada na rota ou tipo
    
    Args:
        route_or_dashboard_type (str): Rota da URL ou tipo do dashboard
    
    Returns:
        str: Código hexadecimal da cor
    """
    # Se é um tipo de dashboard direto (ex: "dashboard1")
    if route_or_dashboard_type in DASHBOARD_COLORS:
        return DASHBOARD_COLORS[route_or_dashboard_type]["color"]
    
    # Se é uma rota, procurar em todas as rotas
    for dashboard_id, config in DASHBOARD_COLORS.items():
        if route_or_dashboard_type in config["routes"]:
            return config["color"]
    
    # Retornar cor padrão se não encontrar
    return DEFAULT_COLOR

def get_dashboard_info(route_or_dashboard_type):
    """
    Retorna informações completas do dashboard
    
    Args:
        route_or_dashboard_type (str): Rota da URL ou tipo do dashboard
    
    Returns:
        dict: Informações do dashboard (color, name, routes)
    """
    # Se é um tipo de dashboard direto
    if route_or_dashboard_type in DASHBOARD_COLORS:
        return DASHBOARD_COLORS[route_or_dashboard_type]
    
    # Se é uma rota, procurar em todas as rotas
    for dashboard_id, config in DASHBOARD_COLORS.items():
        if route_or_dashboard_type in config["routes"]:
            return config
    
    # Retornar configuração padrão se não encontrar
    return {
        "color": DEFAULT_COLOR,
        "name": "GenomasSUS Fenótipos",
        "routes": [route_or_dashboard_type]
    }

def get_all_dashboard_colors():
    """
    Retorna todas as cores dos dashboards para uso nos cards
    
    Returns:
        dict: Mapeamento de dashboard_id para cor
    """
    return {dashboard_id: config["color"] for dashboard_id, config in DASHBOARD_COLORS.items()}

def generate_navbar_style(dashboard_type_or_route):
    """
    Gera o estilo inline para a navbar baseado na cor específica do dashboard

    Args:
        dashboard_type_or_route (str): Tipo do dashboard ou rota

    Returns:
        dict: Estilo CSS para aplicar na navbar
    """
    # Obter a cor específica do dashboard
    dashboard_color = get_dashboard_color(dashboard_type_or_route)

    return {
        'background-color': dashboard_color,
        'color': '#ffffff',  # Texto branco para contraste
        'box-shadow': f'0 2px 10px rgba({int(dashboard_color[1:3], 16)}, {int(dashboard_color[3:5], 16)}, {int(dashboard_color[5:7], 16)}, 0.4)',
        'border-bottom': f'1px solid rgba({int(dashboard_color[1:3], 16)}, {int(dashboard_color[3:5], 16)}, {int(dashboard_color[5:7], 16)}, 0.6)'
    }

# Mapeamento reverso: rota -> dashboard_type para facilitar lookup
ROUTE_TO_DASHBOARD = {}
for dashboard_id, config in DASHBOARD_COLORS.items():
    for route in config["routes"]:
        ROUTE_TO_DASHBOARD[route] = dashboard_id
