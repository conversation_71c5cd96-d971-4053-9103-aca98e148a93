import pandas as pd
from dash import html, dcc, Input, Output
from components.navbar import create_navbar_for_route
from components.dynamic_sidebar import create_sidebar_for_route
from components.footer import footer
import plotly.express as px
import json
import os
from . import DATA_FILE

def get_current_palette():
    # Paletas de cores definidas
    COLOR_PALETTES = {
        "default": ["#6200EA", "#BB86FC", "#03DAC6", "#018786", "#FFEB3B", "#FFC107", "#FF9800", "#F44336", "#E91E63", "#9C27B0", "#673AB7", "#3F51B5"],
        "colorblind": ["#000000", "#E69F00", "#56B4E9", "#009E73", "#F0E442", "#0072B2", "#D55E00", "#CC79A7", "#999999", "#332288", "#44AA99", "#882255"],
        "autistic": ["#7F95D1", "#7FC9D1", "#7FD195", "#D1C97F", "#D17F95", "#957FD1", "#D1957F", "#95D17F", "#7F95D1", "#7FBCD1", "#D17F95", "#957FD1"],
        "monochrome": ["#0D47A1", "#1565C0", "#1976D2", "#1E88E5", "#2196F3", "#42A5F5", "#64B5F6", "#90CAF9", "#BBDEFB", "#E3F2FD", "#2962FF", "#0D47A1"],
        "pastel": ["#FFB6C1", "#FFD700", "#98FB98", "#ADD8E6", "#FFA07A", "#DDA0DD", "#87CEFA", "#F0E68C", "#E6E6FA", "#FFDAB9", "#B0E0E6", "#FFDEAD"]
    }

    config_file = 'config/app_config.json'
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        palette_key = config.get('color_palette', 'default')
    else:
        palette_key = 'default'

    return COLOR_PALETTES.get(palette_key, COLOR_PALETTES['default'])

# Carregamento e processamento dos dados
df = pd.read_csv(DATA_FILE)

# Criar idade a partir do ano de nascimento
current_year = 2025
df['idade'] = current_year - df['qual_o_seu_ano_de_nascimento']
df = df[df['idade'].between(0, 110)]

# Processar variáveis de diabetes
df['diagnostico_diabetes'] = (df['algum_medico_ou_profissional_de_saude_ja_disse_que_voce_tem_diabetes_acucar_no_sangue'] == 'Sim').astype(int)
df['idade_inicio_tratamento_diabetes'] = df['que_idade_voce_tinha_quando_seu_diabetes_foi_tratado_pela_primeira_vez']
df['voce_toma_remedio_para_diabetes'] = (df['voce_toma_remedio_para_diabetes'] == 'Sim').astype(int)

# Calcular IMC
df['imc'] = df['medida_do_peso'] / (df['medida_da_altura'] / 100) ** 2

def create_graph(title, figure, palette_colors=None):
    if palette_colors is None:
        palette_colors = get_current_palette()

    if 'data' in figure and figure['data']:
        for i, trace in enumerate(figure['data']):
            if 'marker' in trace:
                if 'color' in trace['marker'] and isinstance(trace['marker']['color'], str):
                    color_index = i % len(palette_colors)
                    trace['marker']['color'] = palette_colors[color_index]
                elif 'colors' in trace['marker'] and isinstance(trace['marker']['colors'], list):
                    num_colors = len(trace['marker']['colors'])
                    trace['marker']['colors'] = palette_colors[:num_colors]

    return html.Div([
        html.H3(title, style={'textAlign': 'center'}),
        dcc.Graph(figure=figure)
    ], className='graph-container')

# Layout da subpágina de Diabetes
content = html.Div([
    html.H1("Estatísticas Diabetes - RENOMICA-DAC (GS-004)", style={'textAlign': 'center'}),

    # Filtros globais
    html.Div([
        html.Label("Filtrar por Sexo:", style={'marginRight': '10px'}),
        dcc.Dropdown(
            id='sexo-filter-subpage2-dashboard2',
            options=[{'label': 'Todos', 'value': 'all'}] +
                    [{'label': sexo, 'value': sexo} for sexo in ['Masculino', 'Feminino']],
            value='all',
            clearable=False,
            style={'width': '200px'}
        ),
        html.Label("Filtrar por Raça/Cor:", style={'marginLeft': '20px', 'marginRight': '10px'}),
        dcc.Dropdown(
            id='raca-filter-subpage2-dashboard2',
            options=[{'label': 'Todos', 'value': 'all'}] +
                    [{'label': raca, 'value': raca} for raca in df['como_voce_se_considera'].dropna().unique()],
            value='all',
            clearable=False,
            style={'width': '200px'}
        )
    ], style={'display': 'flex', 'justifyContent': 'center', 'marginBottom': '20px'}),

    # Gráficos base (serão atualizados dinamicamente)
    html.Div(id='dynamic-graphs-subpage2-dashboard2')
], className='content')

# Layout principal com a estrutura comum
layout = html.Div(
    className='page-container',
    children=[
        create_navbar_for_route("/dashboard2/renomica-diabetes"),
        html.Div([
            create_sidebar_for_route("/dashboard2/renomica-diabetes"),
            content,
        ], className='main-section', style={'display': 'flex'}),
        footer,
    ],
)

# Callbacks
def register_callbacks(app):
    @app.callback(
        Output('dynamic-graphs-subpage2-dashboard2', 'children'),
        [Input('sexo-filter-subpage2-dashboard2', 'value'), Input('raca-filter-subpage2-dashboard2', 'value')]
    )
    def update_graphs(sexo_filter, raca_filter):
        palette_colors = get_current_palette()

        filtered_df = df.copy()
        if sexo_filter != 'all':
            filtered_df = filtered_df[filtered_df['qual_o_seu_sexo_biologico'] == sexo_filter]

        if raca_filter != 'all':
            filtered_df = filtered_df[filtered_df['como_voce_se_considera'] == raca_filter]

        # Pergunta 18: Prevalência da diabetes no estudo
        diabetes_counts = filtered_df['diagnostico_diabetes'].value_counts()
        labels_diabetes = ['Não Diabético', 'Diabético']
        graph_18 = create_graph("Prevalência da Diabetes no Estudo", {
            'data': [{
                'labels': [labels_diabetes[i] for i in diabetes_counts.index],
                'values': diabetes_counts.values.tolist(),
                'type': 'pie',
                'marker': {'colors': [palette_colors[0], palette_colors[1]]}
            }],
            'layout': {'title': 'Prevalência da Diabetes'}
        }, palette_colors)

        # Pergunta 19: Prevalência da diabetes pela distribuição etária e sexo
        graph_19 = create_graph("Prevalência da Diabetes por Idade e Sexo", {
            'data': [
                {'x': filtered_df[(filtered_df['qual_o_seu_sexo_biologico'] == 'Masculino') & (filtered_df['diagnostico_diabetes'] == 1)]['idade'], 'name': 'Masculino Diabético', 'type': 'histogram', 'marker': {'color': palette_colors[2]}, 'opacity': 0.7},
                {'x': filtered_df[(filtered_df['qual_o_seu_sexo_biologico'] == 'Feminino') & (filtered_df['diagnostico_diabetes'] == 1)]['idade'], 'name': 'Feminino Diabético', 'type': 'histogram', 'marker': {'color': palette_colors[1]}, 'opacity': 0.7}
            ],
            'layout': {'barmode': 'overlay', 'title': 'Prevalência da Diabetes por Idade e Sexo', 'xaxis': {'title': 'Idade (anos)'}, 'yaxis': {'title': 'Número de diabéticos'}}
        }, palette_colors)

        # Pergunta 21: Distribuição de idades em que houve diagnóstico de diabetes
        idades_tratamento = filtered_df[filtered_df['diagnostico_diabetes'] == 1]['idade_inicio_tratamento_diabetes'].dropna()
        graph_21 = create_graph("Distribuição de Idades com Início do Tratamento de Diabetes", {
            'data': [{'x': idades_tratamento, 'type': 'histogram', 'marker': {'color': palette_colors[3]}, 'nbinsx': 15}],
            'layout': {'title': 'Idades no Início do Tratamento de Diabetes', 'xaxis': {'title': 'Idade no início do tratamento (anos)'}, 'yaxis': {'title': 'Número de casos'}}
        }, palette_colors)

        # Pergunta 22: Distribuição das pessoas que possuem diagnóstico de diabetes e tomam medicamento
        medicamento_diabetes_values = [
            filtered_df[(filtered_df['diagnostico_diabetes'] == 1) & (filtered_df['voce_toma_remedio_para_diabetes'] == 0)].shape[0],
            filtered_df[(filtered_df['diagnostico_diabetes'] == 1) & (filtered_df['voce_toma_remedio_para_diabetes'] == 1)].shape[0]
        ]
        medicamento_diabetes_labels = ['Não toma medicamento', 'Toma medicamento']

        graph_22 = create_graph("Pessoas com Diabetes que Tomam Medicamento", {
            'data': [{
                'labels': medicamento_diabetes_labels,
                'values': medicamento_diabetes_values,
                'type': 'pie',
                'marker': {'colors': palette_colors[:2]}
            }],
            'layout': {'title': 'Uso de Medicamento para Diabetes'}
        }, palette_colors)

        # Pergunta 23: Distribuição do IMC dos participantes
        imc_valido = filtered_df['imc'].dropna()
        imc_valido = imc_valido[(imc_valido > 10) & (imc_valido < 60)]  # Filtrar valores extremos
        graph_23 = create_graph("Distribuição do IMC dos Participantes", {
            'data': [{'x': imc_valido, 'type': 'histogram', 'marker': {'color': palette_colors[4]}, 'nbinsx': 20}],
            'layout': {'title': 'Distribuição do IMC', 'xaxis': {'title': 'IMC (kg/m²)'}, 'yaxis': {'title': 'Número de participantes'}}
        }, palette_colors)

        return html.Div([
            html.Div([graph_18, graph_19], className='row'),
            html.Div([graph_21, graph_22], className='row'),
            html.Div([graph_23], className='row')
        ])