#!/usr/bin/env python3
"""
Página do Repositório - GenomaSUS Fenótipos
Documentação técnica central do projeto
"""

from dash import html, dcc, callback, Input, Output, State
from components.footer import footer

def create_repositorio_navbar():
    """Cria navbar customizado apenas para a página repositório"""
    return html.Nav(
        className='navbar',
        style={
            'background-color': '#FFB347',  # Laranja claro - mesma cor do card "Documentações e Repositório GitHub"
            'color': '#ffffff',
            'box-shadow': '0 2px 10px rgba(255, 179, 71, 0.4)',
            'border-bottom': '1px solid rgba(255, 179, 71, 0.6)'
        },
        children=[
            # Botão de toggle para o menu mobile
            html.Button(
                className='menu-toggle',
                id='navbar-toggle-repo',
                children=html.I(className='fa fa-bars'),
            ),

            # Logo e título
            html.Div(
                style={'display': 'flex', 'align-items': 'center'},
                children=[
                    dcc.Link(
                        href='/',
                        children=[
                            html.Img(
                                src='/assets/images/logo_genomasus.png',
                                alt='Logo GenomasSUS',
                                style={'height': '60px', 'margin-right': '16px', 'cursor': 'pointer'},
                            ),
                        ],
                        style={'text-decoration': 'none'}
                    ),
                    html.H1(
                        "Repositório Central de Análises – GenomaSUS Fenótipos",
                        style={
                            'font-size': '20px',
                            'margin': '0',
                            'color': '#1f2937',
                            'font-weight': '600'
                        }
                    ),
                ],
            ),

            # Links de navegação completos
            html.Div(
                className='navbar-links',
                id='navbar-links-repo',
                children=[
                    # Página Inicial
                    dcc.Link(
                        className='nav-btn',
                        href="/",
                        children=[
                            html.I(className="fa fa-home", style={"margin-right": "12px"}),
                            " Página Inicial"
                        ]
                    ),
                    # Sobre o Projeto GenomaSUS
                    dcc.Link(
                        className='nav-btn',
                        href="/sobre",
                        children=[
                            html.I(className="fa fa-info-circle", style={"margin-right": "12px"}),
                            " Sobre o Projeto GenomaSUS"
                        ]
                    ),
                    # Configurações
                    dcc.Link(
                        className='nav-btn',
                        href="/configuracoes",
                        children=[
                            html.I(className="fa fa-cog", style={"margin-right": "12px"}),
                            " Configurações"
                        ]
                    ),
                    # Contato
                    dcc.Link(
                        className='nav-btn',
                        href="sistema/contato",
                        children=[
                            html.I(className="fa fa-question-circle", style={"margin-right": "12px"}),
                            " Contato"
                        ]
                    ),
                    # Botão de logout
                    html.Button(
                        id='logout-button-repo',
                        className='nav-btn logout-btn',
                        children=[
                            html.I(className="fa fa-sign-out-alt", style={"margin-right": "8px"}),
                            " Sair"
                        ],
                        style={
                            'background': 'none',
                            'border': 'none',
                            'color': 'inherit',
                            'cursor': 'pointer',
                            'padding': '6px 12px',
                            'border-radius': '4px',
                            'transition': 'background-color 0.2s',
                            'font-size': '12px',
                            'display': 'flex',
                            'align-items': 'center'
                        }
                    )
                ]
            ),
        ],
    )

def create_collapse_section(section_id, title, content, is_open=False):
    """Cria uma seção colapsável"""
    return html.Div(
        style={
            'backgroundColor': '#ffffff',
            'borderRadius': '12px',
            'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
            'marginBottom': '20px',
            'overflow': 'hidden'
        },
        children=[
            # Cabeçalho clicável
            html.Div(
                id=f'collapse-header-{section_id}',
                style={
                    'padding': '20px',
                    'backgroundColor': '#f8f9fa',
                    'cursor': 'pointer',
                    'borderBottom': '1px solid #dee2e6',
                    'display': 'flex',
                    'justifyContent': 'space-between',
                    'alignItems': 'center',
                    'transition': 'background-color 0.2s'
                },
                children=[
                    html.H3(
                        title,
                        style={
                            'margin': '0',
                            'color': '#000000',
                            'fontSize': '20px',
                            'fontWeight': '600'
                        }
                    ),
                    html.I(
                        id=f'collapse-icon-{section_id}',
                        className='fa fa-chevron-down' if not is_open else 'fa fa-chevron-up',
                        style={'color': '#000000', 'fontSize': '16px'}
                    )
                ]
            ),
            # Conteúdo colapsável
            html.Div(
                id=f'collapse-content-{section_id}',
                style={
                    'padding': '30px' if is_open else '0',
                    'maxHeight': 'none' if is_open else '0',
                    'overflow': 'hidden',
                    'transition': 'all 0.3s ease'
                },
                children=content if is_open else []
            )
        ]
    )

# Conteúdo das seções
def get_sobre_repositorio_content():
    return [
        html.P(
            "Esta seção apresenta o repositório central das análises dos estudos fenotípicos do projeto GenomaSUS – Fenótipos. "
            "O ambiente promove padronização, documentação e reprodutibilidade científica, reunindo os materiais técnicos "
            "desenvolvidos durante o projeto: scripts, notebooks, pipelines, dashboards e diretrizes de boas práticas.",
            style={
                'fontSize': '16px',
                'lineHeight': '1.6',
                'color': '#555',
                'textAlign': 'justify',
                'marginBottom': '0'
            }
        )
    ]

def get_objetivos_escopo_content():
    return [
        html.P(
            "O repositório tem como finalidade facilitar a colaboração entre pesquisadores e equipes técnicas, "
            "por meio da disponibilização organizada e documentada de:",
            style={
                'fontSize': '16px',
                'lineHeight': '1.6',
                'color': '#555',
                'marginBottom': '15px'
            }
        ),
        html.Ul([
            html.Li("Scripts e notebooks utilizados nas análises dos estudos;",
                   style={'marginBottom': '8px', 'fontSize': '16px', 'color': '#555'}),
            html.Li("Estrutura e documentação dos dashboards analíticos interativos;",
                   style={'marginBottom': '8px', 'fontSize': '16px', 'color': '#555'}),
            html.Li("Metodologias aplicadas ao tratamento e à padronização de dados;",
                   style={'marginBottom': '8px', 'fontSize': '16px', 'color': '#555'}),
            html.Li("Documentação detalhada dos estudos: objetivos, métodos e resultados;",
                   style={'marginBottom': '8px', 'fontSize': '16px', 'color': '#555'}),
            html.Li("Ambientes de desenvolvimento voltados à reprodutibilidade, integração e rastreabilidade.",
                   style={'marginBottom': '8px', 'fontSize': '16px', 'color': '#555'}),
        ], style={'paddingLeft': '20px', 'marginBottom': '0'}),
    ]

def get_fluxo_execucao_content():
    return [
        html.P(
            "Esta seção descreve os passos técnicos seguidos, desde a leitura dos dados originais até sua integração ao dashboard analítico:",
            style={
                'fontSize': '16px',
                'lineHeight': '1.6',
                'color': '#555',
                'marginBottom': '15px'
            }
        ),
        html.Ol([
            html.Li("Leitura e análise do banco original no formato CSV;",
                   style={'marginBottom': '8px', 'fontSize': '16px', 'color': '#555'}),
            html.Li("Mapeamento das colunas relevantes com base nos objetivos e variáveis-alvo;",
                   style={'marginBottom': '8px', 'fontSize': '16px', 'color': '#555'}),
            html.Li("Limpeza e padronização dos dados brutos (eliminação de colunas vazias, padronização de nomes, tratamento de valores faltantes);",
                   style={'marginBottom': '8px', 'fontSize': '16px', 'color': '#555'}),
            html.Li("Conversão de formatos e padronização de categorias;",
                   style={'marginBottom': '8px', 'fontSize': '16px', 'color': '#555'}),
            html.Li("Tipagem de colunas (int, float, bool, category);",
                   style={'marginBottom': '8px', 'fontSize': '16px', 'color': '#555'}),
            html.Li("Aplicação de codificação one-hot e verificação de duplicatas;",
                   style={'marginBottom': '8px', 'fontSize': '16px', 'color': '#555'}),
            html.Li("Exportação do dataset processado nos formatos .csv e .xlsx;",
                   style={'marginBottom': '8px', 'fontSize': '16px', 'color': '#555'}),
            html.Li("Integração no dashboard interativo com filtros, métricas e visualizações.",
                   style={'marginBottom': '8px', 'fontSize': '16px', 'color': '#555'}),
        ], style={'paddingLeft': '20px', 'marginBottom': '0'}),
    ]

def get_metodologias_aplicadas_content():
    return [
        html.P(
            "Foram empregadas metodologias consistentes de Engenharia de Dados e práticas robustas de análise de dados clínicos:",
            style={
                'fontSize': '16px',
                'lineHeight': '1.6',
                'color': '#555',
                'marginBottom': '15px'
            }
        ),
        html.Ul([
            html.Li("Exclusão de variáveis não informativas com base nos critérios definidos;",
                   style={'marginBottom': '8px', 'fontSize': '16px', 'color': '#555'}),
            html.Li("Detecção e limpeza de colunas com dados codificados ou inconsistentes (e.g., 999, 888);",
                   style={'marginBottom': '8px', 'fontSize': '16px', 'color': '#555'}),
            html.Li("Conversão de datas, categorização técnica e padronização semântica;",
                   style={'marginBottom': '8px', 'fontSize': '16px', 'color': '#555'}),
            html.Li("Tipagem e encoding controlados para compatibilidade com os modelos analíticos;",
                   style={'marginBottom': '8px', 'fontSize': '16px', 'color': '#555'}),
            html.Li(
                "Ferramentas utilizadas: Python (Pandas, NumPy), Jupyter Notebook, Plotly.",
                style={'marginBottom': '8px', 'fontSize': '16px', 'color': '#555'}
            ),
        ], style={'paddingLeft': '20px', 'marginBottom': '0'}),
    ]

def get_boas_praticas_content():
    return [
        html.P(
            "As práticas adotadas garantem alta qualidade técnica e reprodutibilidade:",
            style={
                'fontSize': '16px',
                'lineHeight': '1.6',
                'color': '#555',
                'marginBottom': '15px'
            }
        ),
        html.Ul([
            html.Li("Organização da estrutura em diretórios por etapa (raw, processed, scripts, notebooks);",
                   style={'marginBottom': '8px', 'fontSize': '16px', 'color': '#555'}),
            html.Li("Nomeação técnica em snake_case, sem acentos nem caracteres especiais;",
                   style={'marginBottom': '8px', 'fontSize': '16px', 'color': '#555'}),
            html.Li("Armazenamento em múltiplos formatos (.csv, .xlsx, .parquet);",
                   style={'marginBottom': '8px', 'fontSize': '16px', 'color': '#555'}),
            html.Li("Controle de versão de scripts e notebooks com histórico e revisão;",
                   style={'marginBottom': '8px', 'fontSize': '16px', 'color': '#555'}),
            html.Li("Comentários descritivos em código e documentação para manutenção contínua.",
                   style={'marginBottom': '8px', 'fontSize': '16px', 'color': '#555'}),
        ], style={'paddingLeft': '20px', 'marginBottom': '0'}),
    ]

# Layout da página "Repositório"
layout = html.Div(
    className='page-container',
    children=[
        # Adicionar o css para o Font Awesome
        html.Link(
            rel='stylesheet',
            href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
        ),

        # Navbar customizado
        create_repositorio_navbar(),

        # Conteúdo principal (sem sidebar)
        html.Div(
            style={
                'backgroundColor': '#f9f9f9',
                'minHeight': '100vh',
                'padding': '40px 20px',
                'fontFamily': 'Arial, sans-serif',
            },
            children=[
                # Container centralizado
                html.Div(
                    style={
                        'maxWidth': '1000px',
                        'margin': '0 auto',
                    },
                    children=[
                        # Título principal e subtítulo
                        html.Div(
                            style={
                                'textAlign': 'center',
                                'marginBottom': '40px',
                            },
                            children=[
                                html.H1(
                                    "Repositório Central de Análises – GenomaSUS Fenótipos",
                                    style={
                                        'color': '#000000',
                                        'fontSize': 'clamp(24px, 5vw, 36px)',
                                        'fontWeight': '600',
                                        'marginBottom': '15px',
                                        'lineHeight': '1.2'
                                    }
                                ),
                                html.P(
                                    "Documentação técnica, scripts, notebooks e dashboards desenvolvidos nas análises fenotípicas do projeto GenomaSUS.",
                                    style={
                                        'fontSize': '18px',
                                        'color': '#666',
                                        'lineHeight': '1.5',
                                        'maxWidth': '800px',
                                        'margin': '0 auto'
                                    }
                                )
                            ]
                        ),

                        # Botões de ação
                        html.Div(
                            style={
                                'display': 'flex',
                                'justifyContent': 'center',
                                'gap': '20px',
                                'marginBottom': '30px',
                                'flexWrap': 'wrap'
                            },
                            children=[
                                html.Button(
                                    "📂 Expandir Tudo",
                                    id='expand-all-btn',
                                    style={
                                        'backgroundColor': '#000000',
                                        'color': '#FFFFFF',
                                        'border': 'none',
                                        'padding': '12px 24px',
                                        'borderRadius': '8px',
                                        'fontSize': '16px',
                                        'fontWeight': '600',
                                        'cursor': 'pointer',
                                        'boxShadow': '0 2px 4px rgba(0,0,0,0.1)',
                                        'transition': 'all 0.2s'
                                    }
                                ),
                                html.A(
                                    [
                                        html.I(className="fab fa-github", style={"margin-right": "8px"}),
                                        "Acessar GitHub"
                                    ],
                                    href="https://github.com/GenomasSUS/genoma_sus_fenotipos",
                                    target="_blank",
                                    style={
                                        'backgroundColor': '#FFB347',
                                        'color': 'white',
                                        'border': 'none',
                                        'padding': '12px 24px',
                                        'borderRadius': '8px',
                                        'fontSize': '16px',
                                        'fontWeight': '600',
                                        'cursor': 'pointer',
                                        'boxShadow': '0 2px 4px rgba(0,0,0,0.1)',
                                        'transition': 'all 0.2s',
                                        'textDecoration': 'none',
                                        'display': 'inline-flex',
                                        'alignItems': 'center'
                                    }
                                )
                            ]
                        ),

                        # Seções colapsáveis
                        html.Div(
                            id='collapse-sections',
                            children=[
                                create_collapse_section('sobre', '➤ Sobre o Repositório', get_sobre_repositorio_content()),
                                create_collapse_section('objetivos', '➤ Objetivos e Escopo', get_objetivos_escopo_content()),
                                create_collapse_section('fluxo', '➤ Fluxo de Execução', get_fluxo_execucao_content()),
                                create_collapse_section('metodologias', '➤ Metodologias Aplicadas', get_metodologias_aplicadas_content()),
                                create_collapse_section('praticas', '➤ Boas Práticas e Padronizações', get_boas_praticas_content()),
                            ]
                        ),
                    ]
                )
            ]
        ),
        footer,
    ],
)

# Função para registrar callbacks
def register_callbacks(app):
    """Registra os callbacks para a página repositório"""

    # Callbacks individuais para cada seção
    @app.callback(
        [Output('collapse-content-sobre', 'style'),
         Output('collapse-content-sobre', 'children'),
         Output('collapse-icon-sobre', 'className')],
        [Input('collapse-header-sobre', 'n_clicks')],
        [State('collapse-content-sobre', 'style')],
        prevent_initial_call=True
    )
    def toggle_sobre(n_clicks, current_style):
        if n_clicks is None:
            return current_style, [], 'fa fa-chevron-down'

        is_open = current_style.get('maxHeight') != '0'

        if is_open:
            return {
                'padding': '0',
                'maxHeight': '0',
                'overflow': 'hidden',
                'transition': 'all 0.3s ease'
            }, [], 'fa fa-chevron-down'
        else:
            return {
                'padding': '30px',
                'maxHeight': 'none',
                'overflow': 'hidden',
                'transition': 'all 0.3s ease'
            }, get_sobre_repositorio_content(), 'fa fa-chevron-up'

    @app.callback(
        [Output('collapse-content-objetivos', 'style'),
         Output('collapse-content-objetivos', 'children'),
         Output('collapse-icon-objetivos', 'className')],
        [Input('collapse-header-objetivos', 'n_clicks')],
        [State('collapse-content-objetivos', 'style')],
        prevent_initial_call=True
    )
    def toggle_objetivos(n_clicks, current_style):
        if n_clicks is None:
            return current_style, [], 'fa fa-chevron-down'

        is_open = current_style.get('maxHeight') != '0'

        if is_open:
            return {
                'padding': '0',
                'maxHeight': '0',
                'overflow': 'hidden',
                'transition': 'all 0.3s ease'
            }, [], 'fa fa-chevron-down'
        else:
            return {
                'padding': '30px',
                'maxHeight': 'none',
                'overflow': 'hidden',
                'transition': 'all 0.3s ease'
            }, get_objetivos_escopo_content(), 'fa fa-chevron-up'

    @app.callback(
        [Output('collapse-content-fluxo', 'style'),
         Output('collapse-content-fluxo', 'children'),
         Output('collapse-icon-fluxo', 'className')],
        [Input('collapse-header-fluxo', 'n_clicks')],
        [State('collapse-content-fluxo', 'style')],
        prevent_initial_call=True
    )
    def toggle_fluxo(n_clicks, current_style):
        if n_clicks is None:
            return current_style, [], 'fa fa-chevron-down'

        is_open = current_style.get('maxHeight') != '0'

        if is_open:
            return {
                'padding': '0',
                'maxHeight': '0',
                'overflow': 'hidden',
                'transition': 'all 0.3s ease'
            }, [], 'fa fa-chevron-down'
        else:
            return {
                'padding': '30px',
                'maxHeight': 'none',
                'overflow': 'hidden',
                'transition': 'all 0.3s ease'
            }, get_fluxo_execucao_content(), 'fa fa-chevron-up'

    @app.callback(
        [Output('collapse-content-metodologias', 'style'),
         Output('collapse-content-metodologias', 'children'),
         Output('collapse-icon-metodologias', 'className')],
        [Input('collapse-header-metodologias', 'n_clicks')],
        [State('collapse-content-metodologias', 'style')],
        prevent_initial_call=True
    )
    def toggle_metodologias(n_clicks, current_style):
        if n_clicks is None:
            return current_style, [], 'fa fa-chevron-down'

        is_open = current_style.get('maxHeight') != '0'

        if is_open:
            return {
                'padding': '0',
                'maxHeight': '0',
                'overflow': 'hidden',
                'transition': 'all 0.3s ease'
            }, [], 'fa fa-chevron-down'
        else:
            return {
                'padding': '30px',
                'maxHeight': 'none',
                'overflow': 'hidden',
                'transition': 'all 0.3s ease'
            }, get_metodologias_aplicadas_content(), 'fa fa-chevron-up'

    @app.callback(
        [Output('collapse-content-praticas', 'style'),
         Output('collapse-content-praticas', 'children'),
         Output('collapse-icon-praticas', 'className')],
        [Input('collapse-header-praticas', 'n_clicks')],
        [State('collapse-content-praticas', 'style')],
        prevent_initial_call=True
    )
    def toggle_praticas(n_clicks, current_style):
        if n_clicks is None:
            return current_style, [], 'fa fa-chevron-down'

        is_open = current_style.get('maxHeight') != '0'

        if is_open:
            return {
                'padding': '0',
                'maxHeight': '0',
                'overflow': 'hidden',
                'transition': 'all 0.3s ease'
            }, [], 'fa fa-chevron-down'
        else:
            return {
                'padding': '30px',
                'maxHeight': 'none',
                'overflow': 'hidden',
                'transition': 'all 0.3s ease'
            }, get_boas_praticas_content(), 'fa fa-chevron-up'

    # Callback para o botão "Expandir Tudo"
    @app.callback(
        [Output('collapse-content-sobre', 'style', allow_duplicate=True),
         Output('collapse-content-sobre', 'children', allow_duplicate=True),
         Output('collapse-icon-sobre', 'className', allow_duplicate=True),
         Output('collapse-content-objetivos', 'style', allow_duplicate=True),
         Output('collapse-content-objetivos', 'children', allow_duplicate=True),
         Output('collapse-icon-objetivos', 'className', allow_duplicate=True),
         Output('collapse-content-fluxo', 'style', allow_duplicate=True),
         Output('collapse-content-fluxo', 'children', allow_duplicate=True),
         Output('collapse-icon-fluxo', 'className', allow_duplicate=True),
         Output('collapse-content-metodologias', 'style', allow_duplicate=True),
         Output('collapse-content-metodologias', 'children', allow_duplicate=True),
         Output('collapse-icon-metodologias', 'className', allow_duplicate=True),
         Output('collapse-content-praticas', 'style', allow_duplicate=True),
         Output('collapse-content-praticas', 'children', allow_duplicate=True),
         Output('collapse-icon-praticas', 'className', allow_duplicate=True),
         Output('expand-all-btn', 'children')],
        [Input('expand-all-btn', 'n_clicks')],
        prevent_initial_call=True
    )
    def expand_all_sections(n_clicks):
        if n_clicks is None:
            return [{}] * 16

        # Estilo para seções abertas
        open_style = {
            'padding': '30px',
            'maxHeight': 'none',
            'overflow': 'hidden',
            'transition': 'all 0.3s ease'
        }

        # Ícone para seções abertas
        open_icon = 'fa fa-chevron-up'

        # Conteúdos
        sobre_content = get_sobre_repositorio_content()
        objetivos_content = get_objetivos_escopo_content()
        fluxo_content = get_fluxo_execucao_content()
        metodologias_content = get_metodologias_aplicadas_content()
        praticas_content = get_boas_praticas_content()

        # Texto do botão
        button_text = "📁 Recolher Tudo" if n_clicks % 2 == 1 else "📂 Expandir Tudo"

        if n_clicks % 2 == 1:  # Expandir
            return (
                open_style, sobre_content, open_icon,
                open_style, objetivos_content, open_icon,
                open_style, fluxo_content, open_icon,
                open_style, metodologias_content, open_icon,
                open_style, praticas_content, open_icon,
                button_text
            )
        else:  # Recolher
            closed_style = {
                'padding': '0',
                'maxHeight': '0',
                'overflow': 'hidden',
                'transition': 'all 0.3s ease'
            }
            closed_icon = 'fa fa-chevron-down'

            return (
                closed_style, [], closed_icon,
                closed_style, [], closed_icon,
                closed_style, [], closed_icon,
                closed_style, [], closed_icon,
                closed_style, [], closed_icon,
                button_text
            )
