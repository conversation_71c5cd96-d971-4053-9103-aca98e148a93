#!/usr/bin/env python3
"""
Página de Dimensões de Qualidade do Estudo SABE
Contém documentação sobre qualidade e integridade dos dados
"""

from dash import html, dcc
from components.navbar import create_navbar_for_route
from components.dynamic_sidebar import create_sidebar_for_route
from components.footer import footer

# Conteúdo da página de dimensões de qualidade
content = html.Div(
    className='content',
    style={
        'padding': '40px 20px',
        'maxWidth': '1200px',
        'margin': '0 auto',
        'lineHeight': '1.8',
        'color': '#333',
        'backgroundColor': '#f9f9f9',
        'fontFamily': 'Arial, sans-serif',
    },
    children=[
        # Título principal
        html.H1(
            "Dimensões de Qualidade – Estudo GS-009 SABE",
            style={
                'textAlign': 'center',
                'color': '#007BFF',
                'marginBottom': '40px',
                'fontSize': 'clamp(24px, 5vw, 36px)',
                'fontWeight': '600',
            }
        ),

        # Descrição específica do estudo
        html.Div(
            style={
                'backgroundColor': '#ffffff',
                'padding': '20px',
                'borderRadius': '10px',
                'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                'marginBottom': '40px',
            },
            children=[
                html.P(
                    "O estudo GS-009 SABE (Saúde, Bem-estar e Envelhecimento) representa um marco na pesquisa epidemiológica brasileira sobre envelhecimento, sendo parte de um estudo multicêntrico internacional coordenado pela Organização Pan-Americana da Saúde (OPAS). Com 1.199 participantes e 268 variáveis coletadas, este estudo longitudinal fornece dados fundamentais sobre as condições de saúde, características sociodemográficas e qualidade de vida da população idosa brasileira. A qualidade dos dados é essencial para garantir a validade das análises epidemiológicas e a aplicabilidade dos resultados para políticas públicas de saúde do idoso.",
                    style={'color': '#333'}
                ),
                html.P(
                    "As dimensões de qualidade aplicadas ao estudo SABE consideram as especificidades da pesquisa gerontológica, incluindo a precisão dos dados de saúde autorreportados, a consistência das informações sociodemográficas ao longo do tempo, e a representatividade da amostra em relação à população idosa brasileira. Com uma taxa de completude geral de 89.7%, o estudo demonstra excelente qualidade na coleta e processamento dos dados, essencial para análises robustas sobre envelhecimento e saúde pública.",
                    style={'color': '#333'}
                ),
                html.P(
                    "As métricas de qualidade são os indicadores mensuráveis que avaliam cada uma dessas dimensões, permitindo quantificar e monitorar se os dados atendem aos padrões estabelecidos, além de identificar discrepâncias que possam comprometer análises robustas. As métricas descritas a seguir foram cuidadosamente estudadas, discutidas e selecionadas por nossa equipe, oferecendo uma abordagem robusta e alinhada com os desafios específicos do Genoma SUS Brasil, garantindo segurança, ética e um impacto positivo nos resultados em saúde pública.",
                    style={'color': '#333'}
                ),
            ],
        ),

        # Características específicas do estudo
        html.Div(
            style={
                'backgroundColor': '#ffffff',
                'padding': '20px',
                'borderRadius': '10px',
                'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                'marginBottom': '40px',
            },
            children=[
                html.H2("Características de Qualidade do Estudo GS-009 SABE", style={'color': '#007BFF'}),
                html.Ul(
                    style={'listStyleType': 'disc', 'paddingLeft': '20px'},
                    children=[
                        html.Li([
                            html.Span(html.Strong("Completude Longitudinal: "), style={'color': '#333'}),
                            html.Span("1.199 participantes com dados completos de seguimento longitudinal, representando a população idosa brasileira.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Consistência Temporal: "), style={'color': '#333'}),
                            html.Span("Dados de ano de nascimento validados e consistentes com idades reportadas, permitindo análises etárias precisas.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Representatividade Geográfica: "), style={'color': '#333'}),
                            html.Span("Amostra representativa da população idosa urbana brasileira, com diversidade socioeconômica e educacional.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Conformidade Epidemiológica: "), style={'color': '#333'}),
                            html.Span("Dados de saúde coletados seguindo protocolos padronizados da OPAS para estudos de envelhecimento.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Contextualização Gerontológica: "), style={'color': '#333'}),
                            html.Span("Análise integrada de fatores sociais, econômicos e de saúde relevantes para o envelhecimento saudável.", style={'color': '#333'})
                        ]),
                    ]
                ),
            ],
        ),

        # Limitações e considerações
        html.Div(
            style={
                'backgroundColor': '#ffffff',
                'padding': '20px',
                'borderRadius': '10px',
                'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                'marginBottom': '40px',
            },
            children=[
                html.H2("Limitações e Considerações de Qualidade", style={'color': '#FF9800'}),
                html.P(
                    "Embora o estudo SABE mantenha altos padrões de qualidade, algumas limitações devem ser consideradas na interpretação dos dados:",
                    style={'color': '#333'}
                ),
                html.Ul(
                    style={'listStyleType': 'disc', 'paddingLeft': '20px'},
                    children=[
                        html.Li([
                            html.Span(html.Strong("Dados Autorreportados: "), style={'color': '#FF9800'}),
                            html.Span("Algumas informações de saúde são baseadas em autorrelato, podendo apresentar viés de memória ou desejabilidade social.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Variabilidade Temporal: "), style={'color': '#FF9800'}),
                            html.Span("Dados coletados em 2000 podem não refletir mudanças contemporâneas nos padrões de saúde e envelhecimento.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Representatividade Urbana: "), style={'color': '#FF9800'}),
                            html.Span("O estudo foca na população urbana, limitando a generalização para áreas rurais.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Completude Variável: "), style={'color': '#FF9800'}),
                            html.Span("Taxa de completude de 89.7% indica presença de dados missing em algumas variáveis específicas.", style={'color': '#333'})
                        ]),
                    ]
                ),
            ],
        ),

        # Dimensões aplicadas
        html.Div(
            style={
                'backgroundColor': '#ffffff',
                'padding': '20px',
                'borderRadius': '10px',
                'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                'marginBottom': '40px',
            },
            children=[
                html.H2("Dimensões de Qualidade Aplicadas", style={'color': '#007BFF'}),
                html.Ul(
                    style={'listStyleType': 'disc', 'paddingLeft': '20px'},
                    children=[
                        html.Li([
                            html.Span(html.Strong("Completude: "), style={'color': '#333'}),
                            html.Span("Verificação da presença de todos os dados esperados para análises epidemiológicas de envelhecimento.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Conformidade: "), style={'color': '#333'}),
                            html.Span("Aderência a padrões internacionais estabelecidos pela OPAS para estudos longitudinais de envelhecimento.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Consistência: "), style={'color': '#333'}),
                            html.Span("Uniformidade entre dados demográficos, socioeconômicos e de saúde ao longo do tempo.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Relevância Gerontológica: "), style={'color': '#333'}),
                            html.Span("Adequação dos dados para responder questões sobre saúde, bem-estar e qualidade de vida na terceira idade.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Representatividade Populacional: "), style={'color': '#333'}),
                            html.Span("Reflexo da diversidade socioeconômica e educacional da população idosa brasileira.", style={'color': '#333'})
                        ]),
                    ]
                ),
            ],
        ),

        # Métricas específicas
        html.Div(
            style={
                'backgroundColor': '#ffffff',
                'padding': '20px',
                'borderRadius': '10px',
                'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                'marginBottom': '40px',
            },
            children=[
                html.H2("Métricas de Qualidade Específicas", style={'color': '#007BFF'}),
                html.P(
                    "Para o estudo GS-009 SABE, foram implementadas métricas específicas que consideram:",
                    style={'color': '#333'}
                ),
                html.Ul(
                    style={'listStyleType': 'disc', 'paddingLeft': '20px'},
                    children=[
                        html.Li([
                            html.Span(html.Strong("Taxa de Completude por Variável: "), style={'color': '#333'}),
                            html.Span("Monitoramento da presença de dados em cada uma das 268 variáveis coletadas (89.7% geral).", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Consistência Temporal: "), style={'color': '#333'}),
                            html.Span("Verificação da coerência entre ano de nascimento e idade reportada pelos participantes.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Validação Educacional: "), style={'color': '#333'}),
                            html.Span("Confirmação da adequação entre alfabetização, frequência escolar e anos de educação.", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Integridade Longitudinal: "), style={'color': '#333'}),
                            html.Span("Verificação da manutenção da qualidade dos dados ao longo do seguimento longitudinal.", style={'color': '#333'})
                        ]),
                    ]
                ),
            ],
        ),
    ]
)

# Layout principal com a estrutura comum
layout = html.Div(
    className='page-container',
    children=[
        create_navbar_for_route("/dashboard1/renomica-dimensoes-estudo-sabe"),
        html.Div([
            create_sidebar_for_route("/dashboard1/renomica-dimensoes-estudo-sabe"),
            content,
        ], className='main-section', style={'display': 'flex'}),
        footer,
    ],
)

# Callbacks (vazio para esta página estática)
def register_callbacks(app):
    pass
