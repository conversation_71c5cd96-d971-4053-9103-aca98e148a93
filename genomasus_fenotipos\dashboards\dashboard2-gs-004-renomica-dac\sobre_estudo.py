import pandas as pd
import numpy as np
from dash import html, dcc, Dash, Input, Output, State
from components.navbar import create_navbar_for_route
from components.dynamic_sidebar import create_sidebar_for_route
from components.footer import footer
import json
import os

def get_current_palette():
    COLOR_PALETTES = {
        "default": ["#335C81", "#2B4C6F", "#23415D", "#1B364B", "#132B39", "#0B2027", "#335C81", "#4A6B8A", "#617A93", "#78899C", "#8F98A5", "#A6A7AE"],
        "warm": ["#FF6B6B", "#FF8E53", "#FF922B", "#FD7E14", "#FAB005", "#FCC419", "#FFD43B", "#FFEC99", "#FFF3BF", "#FFF8DB", "#FFF9DB", "#FFFBF0"],
        "cool": ["#51CF66", "#69DB7C", "#8CE99A", "#B2F2BB", "#C0EB75", "#D8F5A2", "#E9FAB8", "#F4FCE3", "#E6FCF5", "#C3FAE8", "#96F2D7", "#63E6BE"],
        "pastel": ["#FFB6C1", "#FFD700", "#98FB98", "#ADD8E6", "#FFA07A", "#DDA0DD", "#87CEFA", "#F0E68C", "#E6E6FA", "#FFDAB9", "#B0E0E6", "#FFDEAD"]
    }
    
    config_file = 'config/app_config.json'
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        palette_key = config.get('color_palette', 'default')
    else:
        palette_key = 'default'
    
    return COLOR_PALETTES.get(palette_key, COLOR_PALETTES['default'])

# Layout da página
layout = html.Div(
    className='page-container',
    children=[
        html.Link(
            rel='stylesheet',
            href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
        ),
        
        create_navbar_for_route("/dashboard2/renomica-sobre-o-estudo"),
        html.Div(
            className='main-wrapper',
            id='main-wrapper',
            children=[
                create_sidebar_for_route("/dashboard2/renomica-sobre-o-estudo"),
                html.Div(
                    className='content',
                    children=[
                        html.Div(
                            style={
                                'padding': '30px',
                                'max-width': '1000px',
                                'margin': '0 auto',
                                'background-color': '#ffffff',
                                'border-radius': '8px',
                                'box-shadow': '0 2px 10px rgba(0,0,0,0.1)',
                                'margin-top': '20px'
                            },
                            children=[
                                # Título da página
                                html.H1(
                                    children=[
                                        html.Div("Sobre o Estudo GS-004 | Renomica-DAC", style={'font-weight': '600', 'color': '#335C81'}),
                                        html.Div("Rede Nacional de Genômica Cardiovascular – Doença Arterial Coronariana", style={'font-size': '0.7em', 'font-weight': '400', 'color': '#666', 'margin-top': '5px'})
                                    ],
                                    style={
                                        'text-align': 'center',
                                        'margin-bottom': '40px',
                                        'padding-bottom': '20px',
                                        'border-bottom': '2px solid #335C81'
                                    }
                                ),
                                
                                # Descrição do estudo
                                html.P(
                                    "A RENOMICA-DAC (Rede Nacional de Genômica Cardiovascular – Doença Arterial Coronariana) é uma iniciativa estratégica vinculada ao Programa Genomas Brasil, com coordenação do Ministério da Saúde e participação de diversas instituições públicas de ensino e pesquisa. O projeto tem como objetivo investigar a base genética de doenças cardiovasculares, com ênfase na Doença Arterial Coronariana (DAC), a fim de promover avanços no diagnóstico, prevenção e tratamento personalizado dentro do Sistema Único de Saúde (SUS). Por meio da coleta e análise de dados genômicos e fenotípicos de pacientes com DAC, o projeto busca integrar informações clínicas com variantes genéticas, permitindo a construção de modelos de risco e diretrizes de cuidado mais precisas. A RENOMICA-DAC representa um passo importante para consolidar a medicina de precisão em cardiologia no Brasil, ampliando a capacidade do SUS em oferecer soluções baseadas em evidências genômicas e promovendo equidade no acesso à inovação em saúde.",
                                    style={
                                        'text-align': 'justify',
                                        'line-height': '1.6',
                                        'font-size': '16px',
                                        'color': '#333',
                                        'margin-bottom': '30px'
                                    }
                                ),
                                
                                # Botão de volta
                                html.Div(
                                    style={'text-align': 'center', 'margin-top': '40px'},
                                    children=[
                                        dcc.Link(
                                            html.Button(
                                                "Voltar aos Dashboards",
                                                style={
                                                    'background-color': '#335C81',
                                                    'color': '#ffffff',
                                                    'padding': '12px 24px',
                                                    'border': 'none',
                                                    'border-radius': '6px',
                                                    'font-size': '16px',
                                                    'font-weight': '500',
                                                    'cursor': 'pointer',
                                                    'transition': 'all 0.3s ease',
                                                    'box-shadow': '0 2px 4px rgba(51, 92, 129, 0.2)',
                                                }
                                            ),
                                            href='/dashboard2/renomica-sociodemografico',
                                            style={'text-decoration': 'none'}
                                        )
                                    ]
                                )
                            ]
                        )
                    ],
                ),
            ],
        ),
        footer,
    ],
)

def register_callbacks(app):
    """Registra callbacks específicos desta página"""
    pass
