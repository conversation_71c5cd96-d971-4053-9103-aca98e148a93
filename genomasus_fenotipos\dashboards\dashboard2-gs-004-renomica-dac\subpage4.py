import pandas as pd
from dash import html, dcc, Input, Output
from components.navbar import create_navbar_for_route
from components.dynamic_sidebar import create_sidebar_for_route
from components.footer import footer
import json
import os
from . import DATA_FILE

def get_current_palette():
    # Paletas de cores definidas
    COLOR_PALETTES = {
        "default": ["#6200EA", "#BB86FC", "#03DAC6", "#018786", "#FFEB3B", "#FFC107", "#FF9800", "#F44336", "#E91E63", "#9C27B0", "#673AB7", "#3F51B5"],
        "colorblind": ["#000000", "#E69F00", "#56B4E9", "#009E73", "#F0E442", "#0072B2", "#D55E00", "#CC79A7", "#999999", "#332288", "#44AA99", "#882255"],
        "autistic": ["#7F95D1", "#7FC9D1", "#7FD195", "#D1C97F", "#D17F95", "#957FD1", "#D1957F", "#95D17F", "#7F95D1", "#7FBCD1", "#D17F95", "#957FD1"],
        "monochrome": ["#0D47A1", "#1565C0", "#1976D2", "#1E88E5", "#2196F3", "#42A5F5", "#64B5F6", "#90CAF9", "#BBDEFB", "#E3F2FD", "#2962FF", "#0D47A1"],
        "pastel": ["#FFB6C1", "#FFD700", "#98FB98", "#ADD8E6", "#FFA07A", "#DDA0DD", "#87CEFA", "#F0E68C", "#E6E6FA", "#FFDAB9", "#B0E0E6", "#FFDEAD"]
    }
    
    config_file = 'config/app_config.json'
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        palette_key = config.get('color_palette', 'default')
    else:
        palette_key = 'default'
    
    return COLOR_PALETTES.get(palette_key, COLOR_PALETTES['default'])

# Carregamento e processamento dos dados
df = pd.read_csv(DATA_FILE)

# Criar idade a partir do ano de nascimento
current_year = 2025
df['idade'] = current_year - df['qual_o_seu_ano_de_nascimento']
df = df[df['idade'].between(0, 110)]

# Processar variáveis cardiovasculares
df['fez_cirurgia_revascularizacao'] = (df['o_paciente_fez_cirurgia_de_revascularizacao_miocardica'] == 'Sim').astype(int)
df['teve_infarto'] = (df['alguma_vez_um_medico_ou_profissional_de_saude_disse_que_voce_teve_um_infarto_do_miocardio_ou_ataque_cardiaco'] == 'Sim').astype(int)
df['tem_angina'] = (df['algum_medico_ou_profissional_de_saude_ja_disse_que_voce_tem_angina'] == 'Sim').astype(int)
df['teve_fibrilacao_atrial'] = (df['alguem_ja_disse_que_voce_tem_teve_uma_arritmia_chamada_fibrilacao_atrial'] == 'Sim').astype(int)
df['tem_marcapasso'] = (df['voce_tem_um_marcapasso_permanente'] == 'Sim').astype(int)
df['teve_febre_reumatica'] = (df['algum_medico_ja_disse_que_voce_tinha_febre_reumatica'] == 'Sim').astype(int)

# Processar variáveis de hipertensão e diabetes para análises cruzadas
df['tem_hipertensao'] = (df['algum_profissional_de_saude_ja_disse_que_voce_tem_pressao_alta_ou_hipertensao'] == 'Sim').astype(int)
df['diagnostico_diabetes'] = (df['algum_medico_ou_profissional_de_saude_ja_disse_que_voce_tem_diabetes_acucar_no_sangue'] == 'Sim').astype(int)

def create_graph(title, figure, palette_colors=None):
    if palette_colors is None:
        palette_colors = get_current_palette()
    
    if 'data' in figure and figure['data']:
        for i, trace in enumerate(figure['data']):
            if 'marker' in trace:
                if 'color' in trace['marker'] and isinstance(trace['marker']['color'], str):
                    color_index = i % len(palette_colors)
                    trace['marker']['color'] = palette_colors[color_index]
                elif 'colors' in trace['marker'] and isinstance(trace['marker']['colors'], list):
                    num_colors = len(trace['marker']['colors'])
                    trace['marker']['colors'] = palette_colors[:num_colors]
    
    return html.Div([
        html.H3(title, style={'textAlign': 'center'}),
        dcc.Graph(figure=figure)
    ], className='graph-container')

# Layout da subpágina de Doenças Cardíacas
content = html.Div([
    html.H1("Estatísticas Doenças Cardíacas - RENOMICA-DAC (GS-004)", style={'textAlign': 'center'}),
    
    # Filtros globais
    html.Div([
        html.Label("Filtrar por Sexo:", style={'marginRight': '10px'}),
        dcc.Dropdown(
            id='sexo-filter-subpage4-dashboard2',
            options=[{'label': 'Todos', 'value': 'all'}] + 
                    [{'label': sexo, 'value': sexo} for sexo in ['Masculino', 'Feminino']],
            value='all',
            clearable=False,
            style={'width': '200px'}
        ),
        html.Label("Filtrar por Raça/Cor:", style={'marginLeft': '20px', 'marginRight': '10px'}),
        dcc.Dropdown(
            id='raca-filter-subpage4-dashboard2',
            options=[{'label': 'Todos', 'value': 'all'}] + 
                    [{'label': raca, 'value': raca} for raca in df['como_voce_se_considera'].dropna().unique()],
            value='all',
            clearable=False,
            style={'width': '200px'}
        )
    ], style={'display': 'flex', 'justifyContent': 'center', 'marginBottom': '20px'}),
    
    # Gráficos base (serão atualizados dinamicamente)
    html.Div(id='dynamic-graphs-subpage4-dashboard2')
], className='content')

# Layout principal com a estrutura comum
layout = html.Div(
    className='page-container',
    children=[
        create_navbar_for_route("/dashboard2/renomica-doencas-cardiacas"),
        html.Div([
            create_sidebar_for_route("/dashboard2/renomica-doencas-cardiacas"),
            content,
        ], className='main-section', style={'display': 'flex'}),
        footer,  
    ],
)

# Callbacks
def register_callbacks(app):
    @app.callback(
        Output('dynamic-graphs-subpage4-dashboard2', 'children'),
        [Input('sexo-filter-subpage4-dashboard2', 'value'), Input('raca-filter-subpage4-dashboard2', 'value')]
    )
    def update_graphs(sexo_filter, raca_filter):
        palette_colors = get_current_palette()
        
        filtered_df = df.copy()
        if sexo_filter != 'all':
            filtered_df = filtered_df[filtered_df['qual_o_seu_sexo_biologico'] == sexo_filter]
        
        if raca_filter != 'all':
            filtered_df = filtered_df[filtered_df['como_voce_se_considera'] == raca_filter]

        # Pergunta 30: Prevalência de hipertensão entre participantes que tiveram infarto ou cirurgia de revascularização
        cardiacos = filtered_df[(filtered_df['teve_infarto'] == 1) | (filtered_df['fez_cirurgia_revascularizacao'] == 1)]
        nao_cardiacos = filtered_df[(filtered_df['teve_infarto'] == 0) & (filtered_df['fez_cirurgia_revascularizacao'] == 0)]
        
        hiper_cardiacos = len(cardiacos[cardiacos['tem_hipertensao'] == 1])
        total_cardiacos = len(cardiacos)
        hiper_nao_cardiacos = len(nao_cardiacos[nao_cardiacos['tem_hipertensao'] == 1])
        total_nao_cardiacos = len(nao_cardiacos)
        
        prev_hiper_cardiacos = (hiper_cardiacos / total_cardiacos * 100) if total_cardiacos > 0 else 0
        prev_hiper_nao_cardiacos = (hiper_nao_cardiacos / total_nao_cardiacos * 100) if total_nao_cardiacos > 0 else 0
        
        graph_30 = create_graph("Prevalência de Hipertensão: Infarto/Revascularização vs Controles", {
            'data': [{
                'x': ['Com Infarto/Revascularização', 'Sem Infarto/Revascularização'],
                'y': [prev_hiper_cardiacos, prev_hiper_nao_cardiacos],
                'type': 'bar',
                'marker': {'color': [palette_colors[0], palette_colors[1]]}
            }],
            'layout': {'title': 'Prevalência de Hipertensão por História Cardíaca', 'xaxis': {'title': 'Grupo'}, 'yaxis': {'title': 'Prevalência (%)'}}
        }, palette_colors)

        # Pergunta 31: Prevalência de angina entre hipertensos vs não hipertensos
        hipertensos = filtered_df[filtered_df['tem_hipertensao'] == 1]
        nao_hipertensos = filtered_df[filtered_df['tem_hipertensao'] == 0]
        
        angina_hipertensos = len(hipertensos[hipertensos['tem_angina'] == 1])
        total_hipertensos = len(hipertensos)
        angina_nao_hipertensos = len(nao_hipertensos[nao_hipertensos['tem_angina'] == 1])
        total_nao_hipertensos = len(nao_hipertensos)
        
        prev_angina_hipertensos = (angina_hipertensos / total_hipertensos * 100) if total_hipertensos > 0 else 0
        prev_angina_nao_hipertensos = (angina_nao_hipertensos / total_nao_hipertensos * 100) if total_nao_hipertensos > 0 else 0
        
        graph_31 = create_graph("Prevalência de Angina: Hipertensos vs Não Hipertensos", {
            'data': [{
                'x': ['Hipertensos', 'Não Hipertensos'],
                'y': [prev_angina_hipertensos, prev_angina_nao_hipertensos],
                'type': 'bar',
                'marker': {'color': [palette_colors[2], palette_colors[3]]}
            }],
            'layout': {'title': 'Prevalência de Angina por Status de Hipertensão', 'xaxis': {'title': 'Grupo'}, 'yaxis': {'title': 'Prevalência (%)'}}
        }, palette_colors)

        # Pergunta 32: Prevalência de fibrilação atrial entre hipertensos vs não hipertensos
        fa_hipertensos = len(hipertensos[hipertensos['teve_fibrilacao_atrial'] == 1])
        fa_nao_hipertensos = len(nao_hipertensos[nao_hipertensos['teve_fibrilacao_atrial'] == 1])
        
        prev_fa_hipertensos = (fa_hipertensos / total_hipertensos * 100) if total_hipertensos > 0 else 0
        prev_fa_nao_hipertensos = (fa_nao_hipertensos / total_nao_hipertensos * 100) if total_nao_hipertensos > 0 else 0
        
        graph_32 = create_graph("Prevalência de Fibrilação Atrial: Hipertensos vs Não Hipertensos", {
            'data': [{
                'x': ['Hipertensos', 'Não Hipertensos'],
                'y': [prev_fa_hipertensos, prev_fa_nao_hipertensos],
                'type': 'bar',
                'marker': {'color': [palette_colors[4], palette_colors[5]]}
            }],
            'layout': {
                'title': 'Prevalência de Fibrilação Atrial por Status de Hipertensão',
                'xaxis': {'title': 'Grupo'},
                'yaxis': {'title': 'Prevalência (%)'},
                'annotations': [
                    {
                        'text': 'Sem registros',
                        'x': 'Não Hipertensos',
                        'y': 1,
                        'showarrow': False,
                        'font': {'size': 12, 'color': 'gray'},
                        'xanchor': 'center'
                    }
                ] if prev_fa_nao_hipertensos == 0 else []
            }
        }, palette_colors)

        # Pergunta 33: Prevalência de hipertensão entre participantes com marcapasso
        com_marcapasso = filtered_df[filtered_df['tem_marcapasso'] == 1]
        sem_marcapasso = filtered_df[filtered_df['tem_marcapasso'] == 0]
        
        hiper_com_marcapasso = len(com_marcapasso[com_marcapasso['tem_hipertensao'] == 1])
        total_com_marcapasso = len(com_marcapasso)
        hiper_sem_marcapasso = len(sem_marcapasso[sem_marcapasso['tem_hipertensao'] == 1])
        total_sem_marcapasso = len(sem_marcapasso)
        
        prev_hiper_com_marcapasso = (hiper_com_marcapasso / total_com_marcapasso * 100) if total_com_marcapasso > 0 else 0
        prev_hiper_sem_marcapasso = (hiper_sem_marcapasso / total_sem_marcapasso * 100) if total_sem_marcapasso > 0 else 0
        
        graph_33 = create_graph("Prevalência de Hipertensão: Com Marcapasso vs Sem Marcapasso", {
            'data': [{
                'x': ['Com Marcapasso', 'Sem Marcapasso'],
                'y': [prev_hiper_com_marcapasso, prev_hiper_sem_marcapasso],
                'type': 'bar',
                'marker': {'color': [palette_colors[6], palette_colors[7]]}
            }],
            'layout': {'title': 'Prevalência de Hipertensão por Presença de Marcapasso', 'xaxis': {'title': 'Grupo'}, 'yaxis': {'title': 'Prevalência (%)'}}
        }, palette_colors)

        # Pergunta 34: Prevalência de hipertensão entre participantes com histórico de febre reumática
        com_febre_reumatica = filtered_df[filtered_df['teve_febre_reumatica'] == 1]
        sem_febre_reumatica = filtered_df[filtered_df['teve_febre_reumatica'] == 0]
        
        hiper_com_fr = len(com_febre_reumatica[com_febre_reumatica['tem_hipertensao'] == 1])
        total_com_fr = len(com_febre_reumatica)
        hiper_sem_fr = len(sem_febre_reumatica[sem_febre_reumatica['tem_hipertensao'] == 1])
        total_sem_fr = len(sem_febre_reumatica)
        
        prev_hiper_com_fr = (hiper_com_fr / total_com_fr * 100) if total_com_fr > 0 else 0
        prev_hiper_sem_fr = (hiper_sem_fr / total_sem_fr * 100) if total_sem_fr > 0 else 0
        
        graph_34 = create_graph("Prevalência de Hipertensão: Com Febre Reumática vs Sem Febre Reumática", {
            'data': [{
                'x': ['Com Febre Reumática', 'Sem Febre Reumática'],
                'y': [prev_hiper_com_fr, prev_hiper_sem_fr],
                'type': 'bar',
                'marker': {'color': [palette_colors[8], palette_colors[9]]}
            }],
            'layout': {'title': 'Prevalência de Hipertensão por Histórico de Febre Reumática', 'xaxis': {'title': 'Grupo'}, 'yaxis': {'title': 'Prevalência (%)'}}
        }, palette_colors)

        # Pergunta 35: Prevalência de diabetes entre participantes que tiveram infarto ou cirurgia de revascularização
        diabeticos = filtered_df[filtered_df['diagnostico_diabetes'] == 1]
        nao_diabeticos = filtered_df[filtered_df['diagnostico_diabetes'] == 0]

        diab_cardiacos = len(cardiacos[cardiacos['diagnostico_diabetes'] == 1])
        diab_nao_cardiacos = len(nao_cardiacos[nao_cardiacos['diagnostico_diabetes'] == 1])

        prev_diab_cardiacos = (diab_cardiacos / total_cardiacos * 100) if total_cardiacos > 0 else 0
        prev_diab_nao_cardiacos = (diab_nao_cardiacos / total_nao_cardiacos * 100) if total_nao_cardiacos > 0 else 0

        graph_35 = create_graph("Prevalência de Diabetes: Infarto/Revascularização vs Controles", {
            'data': [{
                'x': ['Com Infarto/Revascularização', 'Sem Infarto/Revascularização'],
                'y': [prev_diab_cardiacos, prev_diab_nao_cardiacos],
                'type': 'bar',
                'marker': {'color': [palette_colors[10], palette_colors[11]]}
            }],
            'layout': {'title': 'Prevalência de Diabetes por História Cardíaca', 'xaxis': {'title': 'Grupo'}, 'yaxis': {'title': 'Prevalência (%)'}}
        }, palette_colors)

        # Pergunta 36: Prevalência de angina entre diabéticos vs não diabéticos
        total_diabeticos = len(diabeticos)
        total_nao_diabeticos = len(nao_diabeticos)

        angina_diabeticos = len(diabeticos[diabeticos['tem_angina'] == 1])
        angina_nao_diabeticos = len(nao_diabeticos[nao_diabeticos['tem_angina'] == 1])

        prev_angina_diabeticos = (angina_diabeticos / total_diabeticos * 100) if total_diabeticos > 0 else 0
        prev_angina_nao_diabeticos = (angina_nao_diabeticos / total_nao_diabeticos * 100) if total_nao_diabeticos > 0 else 0

        graph_36 = create_graph("Prevalência de Angina: Diabéticos vs Não Diabéticos", {
            'data': [{
                'x': ['Diabéticos', 'Não Diabéticos'],
                'y': [prev_angina_diabeticos, prev_angina_nao_diabeticos],
                'type': 'bar',
                'marker': {'color': [palette_colors[0], palette_colors[1]]}
            }],
            'layout': {'title': 'Prevalência de Angina por Status de Diabetes', 'xaxis': {'title': 'Grupo'}, 'yaxis': {'title': 'Prevalência (%)'}}
        }, palette_colors)

        # Pergunta 37: Prevalência de fibrilação atrial entre diabéticos vs não diabéticos
        fa_diabeticos = len(diabeticos[diabeticos['teve_fibrilacao_atrial'] == 1])
        fa_nao_diabeticos = len(nao_diabeticos[nao_diabeticos['teve_fibrilacao_atrial'] == 1])

        prev_fa_diabeticos = (fa_diabeticos / total_diabeticos * 100) if total_diabeticos > 0 else 0
        prev_fa_nao_diabeticos = (fa_nao_diabeticos / total_nao_diabeticos * 100) if total_nao_diabeticos > 0 else 0

        graph_37 = create_graph("Prevalência de Fibrilação Atrial: Diabéticos vs Não Diabéticos", {
            'data': [{
                'x': ['Diabéticos', 'Não Diabéticos'],
                'y': [prev_fa_diabeticos, prev_fa_nao_diabeticos],
                'type': 'bar',
                'marker': {'color': [palette_colors[2], palette_colors[3]]}
            }],
            'layout': {'title': 'Prevalência de Fibrilação Atrial por Status de Diabetes', 'xaxis': {'title': 'Grupo'}, 'yaxis': {'title': 'Prevalência (%)'}}
        }, palette_colors)

        # Pergunta 38: Prevalência de diabetes entre participantes com marcapasso
        diab_com_marcapasso = len(com_marcapasso[com_marcapasso['diagnostico_diabetes'] == 1])
        diab_sem_marcapasso = len(sem_marcapasso[sem_marcapasso['diagnostico_diabetes'] == 1])

        prev_diab_com_marcapasso = (diab_com_marcapasso / total_com_marcapasso * 100) if total_com_marcapasso > 0 else 0
        prev_diab_sem_marcapasso = (diab_sem_marcapasso / total_sem_marcapasso * 100) if total_sem_marcapasso > 0 else 0

        graph_38 = create_graph("Prevalência de Diabetes: Com Marcapasso vs Sem Marcapasso", {
            'data': [{
                'x': ['Com Marcapasso', 'Sem Marcapasso'],
                'y': [prev_diab_com_marcapasso, prev_diab_sem_marcapasso],
                'type': 'bar',
                'marker': {'color': [palette_colors[4], palette_colors[5]]}
            }],
            'layout': {'title': 'Prevalência de Diabetes por Presença de Marcapasso', 'xaxis': {'title': 'Grupo'}, 'yaxis': {'title': 'Prevalência (%)'}}
        }, palette_colors)

        # Pergunta 39: Prevalência de diabetes entre participantes com histórico de febre reumática
        diab_com_fr = len(com_febre_reumatica[com_febre_reumatica['diagnostico_diabetes'] == 1])
        diab_sem_fr = len(sem_febre_reumatica[sem_febre_reumatica['diagnostico_diabetes'] == 1])

        prev_diab_com_fr = (diab_com_fr / total_com_fr * 100) if total_com_fr > 0 else 0
        prev_diab_sem_fr = (diab_sem_fr / total_sem_fr * 100) if total_sem_fr > 0 else 0

        graph_39 = create_graph("Prevalência de Diabetes: Com Febre Reumática vs Sem Febre Reumática", {
            'data': [{
                'x': ['Com Febre Reumática', 'Sem Febre Reumática'],
                'y': [prev_diab_com_fr, prev_diab_sem_fr],
                'type': 'bar',
                'marker': {'color': [palette_colors[6], palette_colors[7]]}
            }],
            'layout': {'title': 'Prevalência de Diabetes por Histórico de Febre Reumática', 'xaxis': {'title': 'Grupo'}, 'yaxis': {'title': 'Prevalência (%)'}}
        }, palette_colors)

        return html.Div([
            html.Div([graph_30, graph_31], className='row'),
            html.Div([graph_32, graph_33], className='row'),
            html.Div([graph_34, graph_35], className='row'),
            html.Div([graph_36, graph_37], className='row'),
            html.Div([graph_38, graph_39], className='row')
        ])
