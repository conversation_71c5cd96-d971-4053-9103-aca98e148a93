#!/usr/bin/env python3

from dash import html, dcc

# Configurações de menu por tipo
SIDEBAR_CONFIGS = {
    "config_only": {
        "show_title": False,
        "items": [
            {"type": "divider", "text": "<PERSON>ste<PERSON>"},
            {"href": "/sistema/configuracoes", "icon": "fa fa-cog", "text": " Configurações de Visualização"}
        ]
    },
    "config": {
        "show_title": True,
        "items": [
            {"type": "divider", "text": "<PERSON><PERSON><PERSON>"},
            {"href": "/sistema/configuracoes", "icon": "fa fa-cog", "text": " Configurações"},
            {"href": "/sistema/contato", "icon": "fa fa-question-circle", "text": " Contato"},
            {"href": "/sobre", "icon": "fa fa-info-circle", "text": " Sobre o Projeto GenomaSUS"}
        ]
    },
    "default": {
        "show_title": True,
        "items": [
            {"href": "/dashboard1/sabe-sociodemografico", "icon": "fa fa-home", "text": " Sociodemográfico"},
            {"href": "/dashboard1/sabe-hipertensao", "icon": "fa fa-chart-line", "text": " Hipertensão"},
            {"href": "/dashboard1/sabe-diabetes", "icon": "fa fa-table", "text": " Diabetes"},
            {"href": "/dashboard1/sabe-sobre-o-estudo", "icon": "fa fa-info-circle", "text": " Sobre o Estudo"}
        ]
    }
}

def _create_menu_items(items):
    """Cria itens de menu dinamicamente"""
    menu_items = []

    for item in items:
        if item.get("type") == "divider":
            menu_items.append(html.Li(
                className='sidebar-divider',
                children=item["text"]
            ))
        else:
            menu_items.append(html.Li(
                dcc.Link(
                    href=item["href"],
                    children=[
                        html.I(className=item["icon"], style={"margin-right": "12px"}),
                        item["text"]
                    ]
                )
            ))

    return menu_items

def create_sidebar(sidebar_type="default"):
    """
    Cria sidebar dinâmico baseado no tipo de página

    Args:
        sidebar_type (str): Tipo do sidebar ("default", "config", "config_only", "none")

    Returns:
        html.Div: Componente sidebar configurado para o contexto específico
    """
    # Retornar div vazia se não houver sidebar
    if sidebar_type == "none":
        return html.Div()

    # Obter configuração do sidebar
    config = SIDEBAR_CONFIGS.get(sidebar_type, SIDEBAR_CONFIGS["default"])
    menu_items = _create_menu_items(config["items"])

    return html.Div(
        className='sidebar',
        id='sidebar',
        children=[
            html.H2("Menu") if config["show_title"] else None,
            html.Ul(
                className='sidebar-menu',
                children=menu_items
            ),
        ],
    )

# Sidebar padrão para compatibilidade com código existente
sidebar = create_sidebar("default")