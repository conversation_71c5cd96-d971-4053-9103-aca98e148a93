import pandas as pd
import numpy as np
from dash import html, dcc, Dash, Input, Output, State
from components.navbar import create_navbar_for_route
from components.dynamic_sidebar import create_sidebar_for_route
from components.footer import footer
import plotly.express as px
import json
import os
from . import DATA_FILE

def get_current_palette():
    # Paletas de cores definidas
    COLOR_PALETTES = {
        "default": ["#6200EA", "#BB86FC", "#03DAC6", "#018786", "#FFEB3B", "#FFC107", "#FF9800", "#F44336", "#E91E63", "#9C27B0", "#673AB7", "#3F51B5"],
        "colorblind": ["#000000", "#E69F00", "#56B4E9", "#009E73", "#F0E442", "#0072B2", "#D55E00", "#CC79A7", "#999999", "#332288", "#44AA99", "#882255"],
        "autistic": ["#7F95D1", "#7FC9D1", "#7FD195", "#D1C97F", "#D17F95", "#957FD1", "#D1957F", "#95D17F", "#7F95D1", "#7FBCD1", "#D17F95", "#957FD1"],
        "monochrome": ["#0D47A1", "#1565C0", "#1976D2", "#1E88E5", "#2196F3", "#42A5F5", "#64B5F6", "#90CAF9", "#BBDEFB", "#E3F2FD", "#2962FF", "#0D47A1"],
        "pastel": ["#FFB6C1", "#FFD700", "#98FB98", "#ADD8E6", "#FFA07A", "#DDA0DD", "#87CEFA", "#F0E68C", "#E6E6FA", "#FFDAB9", "#B0E0E6", "#FFDEAD"]
    }

    config_file = 'config/app_config.json'
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        palette_key = config.get('color_palette', 'default')
    else:
        palette_key = 'default'

    return COLOR_PALETTES.get(palette_key, COLOR_PALETTES['default'])

# Carregamento e processamento dos dados
df = pd.read_csv(DATA_FILE)

# Criar idade a partir do ano de nascimento
current_year = 2025  # Ano atual do estudo
df['idade'] = current_year - df['qual_o_seu_ano_de_nascimento']
df = df[df['idade'].between(0, 110)]

# Criar variáveis binárias para sexo
df['sexo_feminino'] = (df['qual_o_seu_sexo_biologico'] == 'Feminino').astype(int)
df['sexo_masculino'] = (df['qual_o_seu_sexo_biologico'] == 'Masculino').astype(int)

# Criar variáveis binárias para raça/cor
racas = df['como_voce_se_considera'].dropna().unique()
for raca in racas:
    df[f'raca_{raca.lower()}'] = (df['como_voce_se_considera'] == raca).astype(int)

# Processar variáveis de hipertensão
df['tem_hipertensao'] = (df['algum_profissional_de_saude_ja_disse_que_voce_tem_pressao_alta_ou_hipertensao'] == 'Sim').astype(int)
df['idade_diagnostico_hipertensao'] = df['que_idade_voce_tinha_quando_te_disseram_isso_pela_primeira_vez']

# Processar medicamento para hipertensão
df['tomou_remedio_hipertensao_sim_agora'] = (df['alguma_vez_voce_ja_tomou_remedio_para_pressao_alta_ou_hipertensao'] == 'Sim, agora').astype(int)
df['tomou_remedio_hipertensao_sim_mas_nao_agora'] = (df['alguma_vez_voce_ja_tomou_remedio_para_pressao_alta_ou_hipertensao'] == 'Sim, mas não agora').astype(int)
df['tomou_remedio_hipertensao_nao'] = (df['alguma_vez_voce_ja_tomou_remedio_para_pressao_alta_ou_hipertensao'] == 'Não').astype(int)

# Processar variáveis de diabetes
df['diagnostico_diabetes'] = (df['algum_medico_ou_profissional_de_saude_ja_disse_que_voce_tem_diabetes_acucar_no_sangue'] == 'Sim').astype(int)
df['idade_inicio_tratamento_diabetes'] = df['que_idade_voce_tinha_quando_seu_diabetes_foi_tratado_pela_primeira_vez']
df['voce_toma_remedio_para_diabetes'] = (df['voce_toma_remedio_para_diabetes'] == 'Sim').astype(int)

# Calcular IMC
df['imc'] = df['medida_do_peso'] / (df['medida_da_altura'] / 100) ** 2

# Processar variáveis de tabagismo
df['fumou_100_cigarros_vida'] = (df['voce_fumou_pelo_menos_100_cigarros_ou_5_macos_em_toda_a_sua_vida'] == 'Sim').astype(int)

# Processar variáveis de alcoolismo
df['bebeu_alcool_alguma_vez_na_vida'] = (df['em_toda_a_sua_vida_voce_ja_bebeu_pelo_menos_1_dose_de_qualquer_tipo_de_bebida_alcoolica_nao_considerar_pequenos_goles'] == 'Sim').astype(int)

def create_graph(title, figure, palette_colors=None):
    if palette_colors is None:
        palette_colors = get_current_palette()

    if 'data' in figure and figure['data']:
        for i, trace in enumerate(figure['data']):
            if 'marker' in trace:
                if 'color' in trace['marker'] and isinstance(trace['marker']['color'], str):
                    color_index = i % len(palette_colors)
                    trace['marker']['color'] = palette_colors[color_index]
                elif 'colors' in trace['marker'] and isinstance(trace['marker']['colors'], list):
                    num_colors = len(trace['marker']['colors'])
                    trace['marker']['colors'] = palette_colors[:num_colors]

    return html.Div([
        html.H3(title, style={'textAlign': 'center'}),
        dcc.Graph(figure=figure)
    ], className='graph-container')

layout = html.Div(
    className='page-container',
    children=[
        html.Link(
            rel='stylesheet',
            href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
        ),

        create_navbar_for_route("/dashboard2/renomica-sociodemografico"),
        html.Div(
            className='main-wrapper',
            id='main-wrapper',
            children=[
                create_sidebar_for_route("/dashboard2/renomica-sociodemografico"),
                html.Div(
                    className='content',
                    children=[
                        html.Div(id='dashboard2-content'),
                    ],
                ),
            ],
        ),
        footer,
    ],
)

# Filtros globais
filters = html.Div([
    html.Label("Filtrar por Sexo:", style={'marginRight': '10px'}),
    dcc.Dropdown(
        id='sexo-filter-dashboard2',
        options=[{'label': 'Todos', 'value': 'all'}] +
                [{'label': sexo, 'value': sexo} for sexo in ['Masculino', 'Feminino']],
        value='all',
        clearable=False,
        style={'width': '200px'}
    ),
    html.Label("Filtrar por Raça/Cor:", style={'marginLeft': '20px', 'marginRight': '10px'}),
    dcc.Dropdown(
        id='raca-filter-dashboard2',
        options=[{'label': 'Todos', 'value': 'all'}] +
                [{'label': raca, 'value': raca} for raca in df['como_voce_se_considera'].dropna().unique()],
        value='all',
        clearable=False,
        style={'width': '200px'}
    )
], style={'display': 'flex', 'justifyContent': 'center', 'marginBottom': '20px'})

graphs = html.Div(id='dynamic-graphs-dashboard2')

dashboard2_main_content = html.Div([
    html.H1("Estatísticas Sociodemográficas - RENOMICA-DAC (GS-004)", style={'textAlign': 'center'}),
    filters,
    graphs
])

# Callbacks
def register_callbacks(app):
    @app.callback(
        Output('dynamic-graphs-dashboard2', 'children'),
        [Input('sexo-filter-dashboard2', 'value'), Input('raca-filter-dashboard2', 'value')]
    )
    def update_graphs(sexo_filter, raca_filter):
        palette_colors = get_current_palette()

        filtered_df = df.copy()
        if sexo_filter != 'all':
            filtered_df = filtered_df[filtered_df['qual_o_seu_sexo_biologico'] == sexo_filter]

        if raca_filter != 'all':
            filtered_df = filtered_df[filtered_df['como_voce_se_considera'] == raca_filter]

        # Pergunta 1: Distribuição etária dos participantes
        graph_1 = create_graph("Distribuição Etária dos Participantes", {
            'data': [{'x': filtered_df['idade'], 'type': 'histogram', 'marker': {'color': palette_colors[0]}, 'nbinsx': 20}],
            'layout': {'title': 'Distribuição Etária', 'xaxis': {'title': 'Idade (anos)'}, 'yaxis': {'title': 'Número de participantes'}}
        }, palette_colors)

        # Pergunta 2: Distribuição do sexo dos participantes
        sexo_counts = filtered_df['qual_o_seu_sexo_biologico'].value_counts()
        graph_2 = create_graph("Distribuição do Sexo dos Participantes", {
            'data': [{
                'labels': sexo_counts.index.tolist(),
                'values': sexo_counts.values.tolist(),
                'type': 'pie',
                'marker': {'colors': palette_colors[:len(sexo_counts)]}
            }],
            'layout': {'title': 'Distribuição por Sexo'}
        }, palette_colors)

        # Pergunta 3: Distribuição etária por sexo dos participantes
        graph_3 = create_graph("Distribuição Etária por Sexo dos Participantes", {
            'data': [
                {
                    'x': filtered_df[filtered_df['qual_o_seu_sexo_biologico'] == 'Masculino']['idade'],
                    'name': 'Masculino',
                    'type': 'histogram',
                    'marker': {'color': 'rgba(31, 119, 180, 0.7)'},
                    'nbinsx': 15
                },
                {
                    'x': filtered_df[filtered_df['qual_o_seu_sexo_biologico'] == 'Feminino']['idade'],
                    'name': 'Feminino',
                    'type': 'histogram',
                    'marker': {'color': 'rgba(255, 127, 14, 0.7)'},
                    'nbinsx': 15
                }
            ],
            'layout': {
                'barmode': 'overlay',
                'title': 'Distribuição Etária por Sexo',
                'xaxis': {'title': 'Idade (anos)'},
                'yaxis': {'title': 'Número de participantes'},
                'legend': {'orientation': 'h', 'y': -0.15}
            }
        }, palette_colors)

        # Pergunta 7: Distribuição de raça/cor dos participantes
        raca_counts = filtered_df['como_voce_se_considera'].value_counts()
        graph_7 = create_graph("Distribuição de Raça/Cor dos Participantes", {
            'data': [{
                'labels': raca_counts.index.tolist(),
                'values': raca_counts.values.tolist(),
                'type': 'pie',
                'marker': {'colors': palette_colors[:len(raca_counts)]}
            }],
            'layout': {'title': 'Distribuição de Raça/Cor'}
        }, palette_colors)

        # Pergunta 8: Distribuição de raça/cor por sexo dos participantes
        raca_sexo_data = []
        racas_unicas = filtered_df['como_voce_se_considera'].dropna().unique()

        # Paleta de cores específicas para cada combinação raça-sexo
        cores_combinacoes = [
            '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b',
            '#e377c2', '#7f7f7f', '#bcbd22', '#17becf', '#aec7e8', '#ffbb78',
            '#98df8a', '#ff9896', '#c5b0d5', '#c49c94', '#f7b6d2', '#c7c7c7'
        ]

        cor_index = 0

        # Criar dados com cores específicas para cada combinação raça-sexo
        for raca in racas_unicas:
            masculino_count = len(filtered_df[(filtered_df['como_voce_se_considera'] == raca) & (filtered_df['qual_o_seu_sexo_biologico'] == 'Masculino')])
            feminino_count = len(filtered_df[(filtered_df['como_voce_se_considera'] == raca) & (filtered_df['qual_o_seu_sexo_biologico'] == 'Feminino')])

            # Adicionar barra masculina se houver dados
            if masculino_count > 0:
                raca_sexo_data.append({
                    'x': [raca],
                    'y': [masculino_count],
                    'name': f'{raca} – Masculino',
                    'type': 'bar',
                    'marker': {'color': cores_combinacoes[cor_index % len(cores_combinacoes)]},
                    'showlegend': True
                })
                cor_index += 1

            # Adicionar barra feminina se houver dados
            if feminino_count > 0:
                raca_sexo_data.append({
                    'x': [raca],
                    'y': [feminino_count],
                    'name': f'{raca} – Feminino',
                    'type': 'bar',
                    'marker': {'color': cores_combinacoes[cor_index % len(cores_combinacoes)]},
                    'showlegend': True
                })
                cor_index += 1

        graph_8 = create_graph("Distribuição de Raça/Cor por Sexo dos Participantes", {
            'data': raca_sexo_data,
            'layout': {
                'barmode': 'group',
                'title': 'Distribuição de Raça/Cor por Sexo',
                'xaxis': {'title': 'Raça/Cor'},
                'yaxis': {'title': 'Número de participantes'},
                'legend': {
                    'orientation': 'v',
                    'x': 1.02,
                    'y': 1,
                    'bgcolor': 'rgba(255,255,255,0.9)',
                    'bordercolor': 'rgba(0,0,0,0)',
                    'borderwidth': 0,
                    'font': {'size': 10}
                },
                'margin': {'r': 180}
            }
        }, palette_colors)

        return html.Div([
            html.Div([graph_1, graph_2], className='row'),
            html.Div([graph_3, graph_7], className='row'),
            html.Div([graph_8], className='row')
        ])

    @app.callback(
        Output('dashboard2-content', 'children'),
        [Input('url', 'pathname')]
    )
    def display_dashboard2_content(pathname):
        if pathname == '/dashboard2':
            return dashboard2_main_content
        else:
            return dashboard2_main_content