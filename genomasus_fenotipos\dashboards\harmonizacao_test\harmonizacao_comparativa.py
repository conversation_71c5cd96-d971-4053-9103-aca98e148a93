#!/usr/bin/env python3
"""
Página principal de harmonização de variáveis
Dashboard comparativo entre estudos GS-009, GS-004 e GS-032
"""

from dash import html, dcc, callback, Input, Output
import dash
import plotly.graph_objects as go
import pandas as pd
import sys
import os

# Adicionar o diretório raiz ao path para importar config
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.dashboard_colors import DEFAULT_COLOR
from components.navbar import create_navbar_for_route
from components.footer import footer

from .utils import (
    get_harmonized_questions, 
    get_harmonized_data_for_question,
    get_study_summary_stats
)
from .components import (
    create_age_distribution_chart,
    create_sex_distribution_chart,
    create_race_distribution_chart,
    create_income_distribution_chart,
    create_hypertension_prevalence_chart,
    create_diabetes_prevalence_chart
)

def create_summary_cards():
    """
    Cria cards resumo com estatísticas dos estudos
    
    Returns:
        html.Div: Container com cards resumo
    """
    try:
        stats = get_study_summary_stats()
    except Exception as e:
        stats = {
            'GS-009': {'total_participants': 0, 'error': str(e)},
            'GS-004': {'total_participants': 0, 'error': str(e)},
            'GS-032': {'total_participants': 0, 'error': str(e)}
        }
    
    study_info = {
        'GS-009': {'name': 'SABE', 'color': '#ADD8E6'},
        'GS-004': {'name': 'Renomica-DAC', 'color': '#b7d5ac'},
        'GS-032': {'name': 'Farmacogenética', 'color': '#DDA0DD'}
    }
    
    cards = []
    for study, info in study_info.items():
        participant_count = stats.get(study, {}).get('total_participants', 0)
        
        card = html.Div(
            style={
                'background-color': '#ffffff',
                'border-radius': '12px',
                'box-shadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                'padding': '20px',
                'text-align': 'center',
                'margin': '10px',
                'flex': '1',
                'min-width': '200px',
                'border-top': f'4px solid {info["color"]}'
            },
            children=[
                html.H4(
                    study,
                    style={
                        'margin': '0 0 10px 0',
                        'color': '#333',
                        'font-weight': '600'
                    }
                ),
                html.P(
                    info['name'],
                    style={
                        'margin': '0 0 15px 0',
                        'color': '#666',
                        'font-size': '14px'
                    }
                ),
                html.H2(
                    f"{participant_count:,}",
                    style={
                        'margin': '0',
                        'color': info['color'],
                        'font-weight': '700'
                    }
                ),
                html.P(
                    'participantes',
                    style={
                        'margin': '5px 0 0 0',
                        'color': '#666',
                        'font-size': '12px'
                    }
                )
            ]
        )
        cards.append(card)
    
    return html.Div(
        style={
            'display': 'flex',
            'justify-content': 'center',
            'gap': '20px',
            'margin': '20px 0',
            'flex-wrap': 'wrap'
        },
        children=cards
    )

def create_chart_container(chart_id: str, title: str):
    """
    Cria container para um gráfico
    
    Args:
        chart_id (str): ID do gráfico
        title (str): Título do gráfico
    
    Returns:
        html.Div: Container do gráfico
    """
    return html.Div(
        style={
            'background-color': '#ffffff',
            'border-radius': '12px',
            'box-shadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
            'padding': '20px',
            'margin': '20px 0'
        },
        children=[
            dcc.Graph(
                id=chart_id,
                config={
                    'displayModeBar': True,
                    'displaylogo': False,
                    'modeBarButtonsToRemove': ['pan2d', 'lasso2d', 'select2d']
                }
            )
        ]
    )

# Layout principal
layout = html.Div(
    className='page-container',
    children=[
        html.Link(
            rel='stylesheet',
            href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
        ),

        create_navbar_for_route("/harmonizacao/comparativo"),

        html.Div(
            className='main-wrapper',
            id='main-wrapper',
            children=[
                html.Div(
                    className='content',
                    style={
                        'background-color': '#f5f7fa',
                        'min-height': '100vh',
                        'padding': '20px'
                    },
                    children=[
                        # Header
                        html.Div(
                            style={
                                'background-color': '#ffffff',
                                'border-radius': '12px',
                                'box-shadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                                'padding': '30px',
                                'margin-bottom': '20px',
                                'text-align': 'center'
                            },
                            children=[
                                html.H1(
                                    "Harmonização de Variáveis entre Estudos",
                                    style={
                                        'color': '#333',
                                        'font-weight': '600',
                                        'margin': '20px 0 10px 0',
                                        'font-size': '28px'
                                    }
                                ),
                                html.P(
                                    "Comparação de variáveis harmonizadas entre os estudos",
                                    style={
                                        'color': '#666',
                                        'font-size': '16px',
                                        'margin': '0',
                                        'max-width': '800px',
                                        'margin': '0 auto'
                                    }
                                )
                            ]
                        ),
        

        
                        # Gráficos
                        html.Div(
                            style={'max-width': '1200px', 'margin': '0 auto'},
                            children=[
                                create_chart_container('age-distribution-chart', 'Distribuição Etária'),
                                create_chart_container('sex-distribution-chart', 'Distribuição por Sexo'),
                                create_chart_container('race-distribution-chart', 'Distribuição por Raça/Cor'),
                                create_chart_container('income-distribution-chart', 'Distribuição de Renda'),
                                create_chart_container('hypertension-prevalence-chart', 'Prevalência de Hipertensão'),
                                create_chart_container('diabetes-prevalence-chart', 'Prevalência de Diabetes')
                            ]
                        ),

                        # Footer informativo
                        html.Div(
                            style={
                                'background-color': '#ffffff',
                                'border-radius': '12px',
                                'box-shadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                                'padding': '20px',
                                'margin': '20px 0',
                                'text-align': 'center'
                            },
                            children=[
                                html.H4(
                                    "ℹ️ Informações sobre a Harmonização",
                                    style={'color': '#333', 'margin-bottom': '15px'}
                                ),
                                html.P(
                                    "Os dados apresentados foram harmonizados com base no arquivo de correspondência de variáveis entre os estudos. "
                                    "Nem todas as variáveis estão disponíveis em todos os estudos, resultando em gráficos com diferentes números de estudos representados.",
                                    style={
                                        'color': '#666',
                                        'font-size': '14px',
                                        'line-height': '1.5',
                                        'max-width': '800px',
                                        'margin': '0 auto'
                                    }
                                )
                            ]
                        )
                    ]
                ),
            ],
        ),
        footer,
    ],
)

def register_callbacks(app):
    """
    Registra os callbacks para a página de harmonização
    
    Args:
        app: Instância da aplicação Dash
    """
    
    @app.callback(
        Output('age-distribution-chart', 'figure'),
        Input('age-distribution-chart', 'id')
    )
    def update_age_chart(_):
        """Atualiza gráfico de distribuição etária"""
        try:
            question = "Qual a distribuição etária dos participantes?"
            data_dict = get_harmonized_data_for_question(question)
            return create_age_distribution_chart(data_dict)
        except Exception as e:
            # Retornar gráfico vazio em caso de erro
            fig = go.Figure()
            fig.add_annotation(
                text=f"Erro ao carregar dados: {str(e)}",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False, font=dict(size=16, color="red")
            )
            fig.update_layout(
                title="Distribuição Etária dos Participantes por Estudo",
                template='plotly_white',
                height=500
            )
            return fig
    
    @app.callback(
        Output('sex-distribution-chart', 'figure'),
        Input('sex-distribution-chart', 'id')
    )
    def update_sex_chart(_):
        """Atualiza gráfico de distribuição por sexo"""
        try:
            question = "Qual a distribuição do sexo dos participantes?"
            data_dict = get_harmonized_data_for_question(question)
            return create_sex_distribution_chart(data_dict)
        except Exception as e:
            fig = go.Figure()
            fig.add_annotation(
                text=f"Erro ao carregar dados: {str(e)}",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False, font=dict(size=16, color="red")
            )
            fig.update_layout(
                title="Distribuição por Sexo dos Participantes por Estudo",
                template='plotly_white',
                height=500
            )
            return fig
    
    @app.callback(
        Output('race-distribution-chart', 'figure'),
        Input('race-distribution-chart', 'id')
    )
    def update_race_chart(_):
        """Atualiza gráfico de distribuição por raça/cor"""
        try:
            question = "Qual a distribuição de raça/cor dos participantes?"
            data_dict = get_harmonized_data_for_question(question)
            return create_race_distribution_chart(data_dict)
        except Exception as e:
            fig = go.Figure()
            fig.add_annotation(
                text=f"Erro ao carregar dados: {str(e)}",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False, font=dict(size=16, color="red")
            )
            fig.update_layout(
                title="Distribuição por Raça/Cor dos Participantes por Estudo",
                template='plotly_white',
                height=500
            )
            return fig

    @app.callback(
        Output('income-distribution-chart', 'figure'),
        Input('income-distribution-chart', 'id')
    )
    def update_income_chart(_):
        """Atualiza gráfico de distribuição de renda"""
        try:
            question = "Qual a distribuição de renda dos participantes?"
            data_dict = get_harmonized_data_for_question(question)
            return create_income_distribution_chart(data_dict)
        except Exception as e:
            fig = go.Figure()
            fig.add_annotation(
                text=f"Erro ao carregar dados: {str(e)}",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False, font=dict(size=16, color="red")
            )
            fig.update_layout(
                title="Distribuição de Renda dos Participantes por Estudo",
                template='plotly_white',
                height=500
            )
            return fig

    @app.callback(
        Output('hypertension-prevalence-chart', 'figure'),
        Input('hypertension-prevalence-chart', 'id')
    )
    def update_hypertension_chart(_):
        """Atualiza gráfico de prevalência de hipertensão"""
        try:
            question = "Qual a prevalência da hipertensão no estudo?"
            data_dict = get_harmonized_data_for_question(question)
            return create_hypertension_prevalence_chart(data_dict)
        except Exception as e:
            fig = go.Figure()
            fig.add_annotation(
                text=f"Erro ao carregar dados: {str(e)}",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False, font=dict(size=16, color="red")
            )
            fig.update_layout(
                title="Prevalência de Hipertensão por Estudo",
                template='plotly_white',
                height=500
            )
            return fig

    @app.callback(
        Output('diabetes-prevalence-chart', 'figure'),
        Input('diabetes-prevalence-chart', 'id')
    )
    def update_diabetes_chart(_):
        """Atualiza gráfico de prevalência de diabetes"""
        try:
            question = "Qual a prevalência da diabetes no estudo?"
            data_dict = get_harmonized_data_for_question(question)
            return create_diabetes_prevalence_chart(data_dict)
        except Exception as e:
            fig = go.Figure()
            fig.add_annotation(
                text=f"Erro ao carregar dados: {str(e)}",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False, font=dict(size=16, color="red")
            )
            fig.update_layout(
                title="Prevalência de Diabetes por Estudo",
                template='plotly_white',
                height=500
            )
            return fig
