#!/usr/bin/env python3
"""
Aplicação principal do GenomaSUS Fenótipos
Sistema de dashboards para análise de dados genômicos
"""

import dash
from dash import html, dcc
from dash.dependencies import Input, Output, State
import importlib

# Importações de páginas estáticas
from dashboards import home
from dashboards.documentacao_qualidade import layout as doc_layout
from dashboards.configuracoes.main import layout as configuracoes_layout, register_callbacks as register_configuracoes_callbacks
from dashboards.not_found import layout as not_found_layout
from dashboards.sobre import layout as sobre_layout
from dashboards.ajuda import layout as contato_layout
from dashboards.repositorio import layout as repositorio_layout, register_callbacks as register_repositorio_callbacks

# Importação da funcionalidade de harmonização
from dashboards.harmonizacao_test import layout as harmonizacao_layout, register_callbacks as register_harmonizacao_callbacks, sobre_layout as harmonizacao_sobre_layout

# Importação do módulo de autenticação
from auth import (
    layout as login_layout,
    register_callbacks as register_login_callbacks,
    is_session_valid,
    clear_session,
    update_session_activity
)

# Configuração de módulos dos dashboards
DASHBOARD_MODULES = {
    'dashboard1': {
        'base_path': 'dashboards.dashboard1-gs-009-sabe',
        'pages': ['main', 'subpage1', 'subpage2', 'sobre_estudo', 'dimensoes_qualidade']
    },
    'dashboard2': {
        'base_path': 'dashboards.dashboard2-gs-004-renomica-dac',
        'pages': ['main', 'subpage1', 'subpage2', 'subpage3', 'subpage4', 'sobre_estudo', 'dimensoes_qualidade']
    },
    'dashboard3': {
        'base_path': 'dashboards.dashboard3-gs-032-farmacogenetica',
        'pages': ['main', 'subpage1', 'subpage2', 'subpage3', 'subpage4', 'subpage5', 'sobre_estudo', 'dimensoes_qualidade']
    }
}

# Carregamento dinâmico de módulos
def load_dashboard_modules():
    """Carrega dinamicamente todos os módulos dos dashboards"""
    modules = {}

    for dashboard_id, config in DASHBOARD_MODULES.items():
        modules[dashboard_id] = {}
        for page in config['pages']:
            module_path = f"{config['base_path']}.{page}"
            module = importlib.import_module(module_path)
            modules[dashboard_id][page] = {
                'layout': module.layout,
                'register_callbacks': module.register_callbacks
            }

    return modules

# Carregar módulos
dashboard_modules = load_dashboard_modules()

app = dash.Dash(
    __name__, 
    suppress_callback_exceptions=True,
    external_scripts=[
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"
    ]
)

server = app.server

app.layout = html.Div([
    dcc.Location(id='url', refresh=False),
    dcc.Store(id='session-store', data={'is_authenticated': False}, storage_type='session'),
    html.Div(id='page-content')
])

# Mapeamento de rotas para layouts
ROUTE_MAPPING = {
    # Página inicial
    '/': home.layout,

    # Autenticação
    '/login': login_layout,

    # Dashboard 1 (SABE) - Rotas semânticas
    '/dashboard1/sabe-sociodemografico': lambda: dashboard_modules['dashboard1']['main']['layout'],
    '/dashboard1/sabe-hipertensao': lambda: dashboard_modules['dashboard1']['subpage1']['layout'],
    '/dashboard1/sabe-diabetes': lambda: dashboard_modules['dashboard1']['subpage2']['layout'],
    '/dashboard1/sabe-sobre-o-estudo': lambda: dashboard_modules['dashboard1']['sobre_estudo']['layout'],
    '/dashboard1/renomica-dimensoes-estudo-sabe': lambda: dashboard_modules['dashboard1']['dimensoes_qualidade']['layout'],

    # Dashboard 2 (RENOMICA-DAC) - Rotas semânticas
    '/dashboard2/renomica-sociodemografico': lambda: dashboard_modules['dashboard2']['main']['layout'],
    '/dashboard2/renomica-hipertensao': lambda: dashboard_modules['dashboard2']['subpage1']['layout'],
    '/dashboard2/renomica-diabetes': lambda: dashboard_modules['dashboard2']['subpage2']['layout'],
    '/dashboard2/renomica-tabagismo-alcoolismo': lambda: dashboard_modules['dashboard2']['subpage3']['layout'],
    '/dashboard2/renomica-doencas-cardiacas': lambda: dashboard_modules['dashboard2']['subpage4']['layout'],
    '/dashboard2/renomica-sobre-o-estudo': lambda: dashboard_modules['dashboard2']['sobre_estudo']['layout'],
    '/dashboard2/renomica-dimensoes-estudo-renomicadac': lambda: dashboard_modules['dashboard2']['dimensoes_qualidade']['layout'],

    # Dashboard 3 (FARMACOGENÉTICA) - Rotas semânticas
    '/dashboard3/farmacogenetica-sociodemografico': lambda: dashboard_modules['dashboard3']['main']['layout'],
    '/dashboard3/farmacogenetica-hipertensao': lambda: dashboard_modules['dashboard3']['subpage1']['layout'],
    '/dashboard3/farmacogenetica-diabetes': lambda: dashboard_modules['dashboard3']['subpage2']['layout'],
    '/dashboard3/farmacogenetica-tabagismo-alcoolismo': lambda: dashboard_modules['dashboard3']['subpage3']['layout'],
    '/dashboard3/farmacogenetica-clopidogrel-doencas-cardiacas': lambda: dashboard_modules['dashboard3']['subpage4']['layout'],
    '/dashboard3/farmacogenetica-medicamentos-comorbidades': lambda: dashboard_modules['dashboard3']['subpage5']['layout'],
    '/dashboard3/farmacogenetica-sobre-o-estudo': lambda: dashboard_modules['dashboard3']['sobre_estudo']['layout'],
    '/dashboard3/farmacogenetica-dimensoes-estudo': lambda: dashboard_modules['dashboard3']['dimensoes_qualidade']['layout'],

    # Rotas de compatibilidade (antigas)
    '/dashboard1': lambda: dashboard_modules['dashboard1']['main']['layout'],
    '/dashboard1/subpage1': lambda: dashboard_modules['dashboard1']['subpage1']['layout'],
    '/dashboard1/subpage2': lambda: dashboard_modules['dashboard1']['subpage2']['layout'],
    '/dashboard2': lambda: dashboard_modules['dashboard2']['main']['layout'],
    '/dashboard2/subpage1': lambda: dashboard_modules['dashboard2']['subpage1']['layout'],
    '/dashboard2/subpage2': lambda: dashboard_modules['dashboard2']['subpage2']['layout'],
    '/dashboard2/subpage3': lambda: dashboard_modules['dashboard2']['subpage3']['layout'],
    '/dashboard2/subpage4': lambda: dashboard_modules['dashboard2']['subpage4']['layout'],
    '/dashboard3': lambda: dashboard_modules['dashboard3']['main']['layout'],
    '/dashboard3/subpage1': lambda: dashboard_modules['dashboard3']['subpage1']['layout'],
    '/dashboard3/subpage2': lambda: dashboard_modules['dashboard3']['subpage2']['layout'],
    '/dashboard3/subpage3': lambda: dashboard_modules['dashboard3']['subpage3']['layout'],
    '/dashboard3/subpage4': lambda: dashboard_modules['dashboard3']['subpage4']['layout'],
    '/dashboard3/subpage5': lambda: dashboard_modules['dashboard3']['subpage5']['layout'],

    # Sistema
    '/sistema/configuracoes': configuracoes_layout,
    '/sistema/contato': contato_layout,
    '/configuracoes': configuracoes_layout,
    '/ajuda': contato_layout,

    # Outras páginas
    '/sobre': sobre_layout,
    '/repositorio': repositorio_layout,

    # Harmonização de variáveis
    '/harmonizacao/comparativo': harmonizacao_layout,
    '/harmonizacao/sobre': harmonizacao_sobre_layout,
}

@app.callback(
    [Output('page-content', 'children'),
     Output('session-store', 'data', allow_duplicate=True)],
    [Input('url', 'pathname')],
    [State('session-store', 'data')],
    prevent_initial_call=True
)
def display_page(pathname, session_data):
    """Roteamento principal da aplicação com verificação de autenticação"""

    # Rotas públicas que não precisam de autenticação
    public_routes = ['/login']

    # Se a rota é pública, exibir diretamente
    if pathname in public_routes:
        if pathname in ROUTE_MAPPING:
            layout = ROUTE_MAPPING[pathname]
            return (layout() if callable(layout) else layout), session_data

    # Para todas as outras rotas, verificar autenticação
    if not is_session_valid(session_data):
        # Se não está autenticado, retornar layout de redirecionamento
        return html.Div([
            dcc.Location(id='redirect-to-login', pathname='/login', refresh=True),
            html.Div(
                style={'text-align': 'center', 'margin-top': '100px'},
                children=[
                    html.H3("Acesso Restrito", style={'color': '#000000', 'text-align': 'center'}),
                    html.P("Redirecionando para a página de login...", style={'color': '#000000', 'text-align': 'center'}),
                    html.A("Clique aqui se não for redirecionado automaticamente", href="/login", style={'color': '#000000', 'text-align': 'center'})
                ]
            )
        ]), session_data

    # Se está autenticado, atualizar atividade da sessão e verificar se a rota existe
    updated_session = update_session_activity(session_data)

    if pathname in ROUTE_MAPPING:
        layout = ROUTE_MAPPING[pathname]
        return (layout() if callable(layout) else layout), updated_session

    # Página não encontrada
    return not_found_layout, updated_session

# Callback para logout (será registrado apenas quando necessário)
def register_logout_callback(app):
    """Registra o callback de logout"""
    @app.callback(
        [Output('session-store', 'data', allow_duplicate=True),
         Output('url', 'pathname', allow_duplicate=True)],
        [Input('logout-button', 'n_clicks')],
        prevent_initial_call=True
    )
    def handle_logout(n_clicks):
        """Processa o logout do usuário"""
        if n_clicks and n_clicks > 0:
            return clear_session(), '/login'
        return [dash.no_update, dash.no_update]

# Registro automático de callbacks
def register_all_callbacks(app):
    """Registra todos os callbacks dos dashboards"""

    # Callbacks dos dashboards
    for dashboard_id, pages in dashboard_modules.items():
        for page_name, page_data in pages.items():
            page_data['register_callbacks'](app)

    # Callbacks de páginas especiais
    register_configuracoes_callbacks(app)

    # Callbacks de harmonização
    register_harmonizacao_callbacks(app)

    # Callbacks do repositório
    register_repositorio_callbacks(app)

    # Callbacks de autenticação
    register_login_callbacks(app)

    # Callback de logout
    register_logout_callback(app)

# Registrar todos os callbacks
register_all_callbacks(app)

if __name__ == '__main__':
    app.run(debug=True)
