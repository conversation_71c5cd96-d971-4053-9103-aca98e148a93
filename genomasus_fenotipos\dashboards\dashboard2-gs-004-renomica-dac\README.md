# GenomaSUS Fenótipos - Dashboard Renomica-DAC

## Visão Geral do Projeto

O **Dashboard Renomica-DAC** é uma plataforma interativa de visualização de dados desenvolvida no contexto do **GenomaSUS Brasil** - Programa Nacional de Genômica e Saúde de Precisão do Ministério da Saúde. Este dashboard foi especificamente projetado para análise exploratória dos dados do estudo **Renomica-DAC**, fornecendo insights sobre características sociodemográficas, cardiovasculares e metabólicas de 137 participantes com doença arterial coronariana confirmada.

### Objetivos Específicos
- **Visualização Interativa**: Interface responsiva com filtros dinâmicos por sexo e raça/cor
- **Análises Descritivas**: Exploração de variáveis sociodemográficas, hipertensão e diabetes
- **Qualidade Científica**: Dados processados e validados para pesquisa em genômica
- **Acessibilidade**: Suporte a múltiplas paletas de cores incluindo daltonismo
- **Reprodutibilidade**: Documentação completa de todas as transformações aplicadas
- **Resposta a Perguntas de Pesquisa**: Visualizações distribuídas em seções temáticas

## Descrição do Banco de Dados

### Arquivo Principal
**Nome**: `banco_renomicadac_processado_2025_final_dash.csv`
**Status**: Arquivo oficial validado para uso em produção
**Localização**: `dashboards/data/`

### Características do Dataset Final
| Métrica | Valor | Descrição |
|---------|-------|-----------|
| **Participantes** | 137 | Total de indivíduos únicos no estudo |
| **Colunas** | 133 | 125 variáveis originais + 8 derivadas |
| **IDs Únicos** | 137 | Identificadores únicos preservados (100%) |
| **Idade Média** | 65.4 anos | Faixa etária: 42-85 anos (realística) |
| **Altura Média** | 165.4 cm | Valores realísticos pós-limpeza |
| **Peso Médio** | 75.6 kg | Valores realísticos pós-limpeza |
| **IMC Médio** | 27.6 kg/m² | Calculado automaticamente |

### Características Clínicas Validadas
- **Hipertensão**: 89 casos (65.0% da amostra) - Prevalência coerente com literatura
- **Diabetes**: 52 casos (38.0% da amostra) - Prevalência esperada para a população
- **Distribuição por Sexo**: 60% Masculino, 40% Feminino
- **Distribuição Étnica**: Pardo (47%), Branco (38%), Preto (12%), Amarelo (3%)

### Fontes e Contexto dos Dados
- **Origem**: Estudo Renomica-DAC - Doença Arterial Coronariana
- **Tipo**: Dados fenotípicos cardiovasculares e metabólicos
- **Método de Coleta**: Questionários estruturados e exames clínicos padronizados
- **População**: Pacientes com doença arterial coronariana confirmada por cateterismo

## Etapas de Pré-Processamento Detalhadas

### 1. Identificação e Tratamento de Códigos Inválidos

#### Códigos de Missing Identificados e Tratados
```python
888 → "Não se aplica" → Convertido para NaN
999 → "Não sabe/Não informado" → Convertido para NaN
2024 → Erro de digitação em anos → Removido completamente
```

#### Justificativa Técnica Detalhada
- **888**: Representa questões não aplicáveis ao participante específico
- **999**: Indica informação desconhecida, não coletada ou recusada
- **2024**: Anos de nascimento no futuro (biologicamente impossível)
- **Impacto**: Preservação da integridade estatística dos dados

### 2. Correção de Valores Extremos e Outliers Biológicos

#### Altura (medida_da_altura)
```python
Antes: média = 333.7 cm (incluindo valores 999 como altura)
Depois: média = 165.4 cm (valores biologicamente plausíveis)
Critério: Remoção de valores > 250 cm ou < 100 cm
Método: Substituição por NaN e posterior imputação pela mediana
Validação: Distribuição normal confirmada pós-tratamento
```

#### Peso (medida_do_peso)
```python
Antes: média = 212.4 kg (incluindo valores 999 como peso)
Depois: média = 75.6 kg (valores biologicamente realísticos)
Critério: Remoção de valores > 200 kg ou < 30 kg
Método: Substituição por NaN e imputação por regressão linear
Validação: Correlação com altura mantida (r=0.65)
```

#### Ano de Nascimento (qual_o_seu_ano_de_nascimento)
```python
Antes: Incluía valores 2024, 888, 999
Depois: Apenas anos entre 1940-1983 (idades 42-85 anos)
Critério: Remoção de anos impossíveis ou códigos de missing
Método: Validação cruzada com idade reportada
Resultado: Buscando 100% de consistência temporal
```

### 3. Criação de Variáveis Derivadas com Justificativas Científicas

#### Variáveis Calculadas e Validadas
```python
# 1. Idade (derivada do ano de nascimento)
idade = 2025 - qual_o_seu_ano_de_nascimento
# Justificativa: Padronização etária para análises epidemiológicas
# Validação: Correlação r=0.98 com idade auto-reportada

# 2. Índice de Massa Corporal (IMC)
imc = medida_do_peso / (medida_da_altura / 100)²
# Justificativa: Indicador antropométrico padrão OMS
# Validação: Distribuição normal, média 27.6 (sobrepeso leve)

# 3. Hipertensão (variável binária)
tem_hipertensao = 1 se "Sim", 0 se "Não"
# Justificativa: Facilita análises estatísticas e machine learning
# Validação: 65% de prevalência (coerente com literatura DAC)

# 4. Diabetes (variável binária)
diagnostico_diabetes = 1 se "Sim", 0 se "Não"
# Justificativa: Padronização para análises quantitativas
# Validação: 38% de prevalência (esperado para população DAC)

# 5. Tabagismo (variável binária)
fumou_100_cigarros_vida = 1 se "Sim", 0 se "Não"
# Justificativa: Definição epidemiológica padrão CDC/OMS
# Validação: Correlação com histórico cardiovascular

# 6. Consumo de Álcool (variável binária)
bebeu_alcool_alguma_vez_na_vida = 1 se "Sim", 0 se "Não"
# Justificativa: Indicador de exposição cumulativa ao álcool
# Validação: Distribuição esperada para população brasileira

# 7. Idade do Diagnóstico de Hipertensão (limpa)
idade_diagnostico_hipertensao = campo original sem códigos 888/999
# Justificativa: Análises de tempo até diagnóstico
# Validação: Média 52.3 anos (coerente com literatura)

# 8. Idade de Início do Tratamento de Diabetes (limpa)
idade_inicio_tratamento_diabetes = campo original sem códigos 888/999
# Justificativa: Análise de tempo até intervenção terapêutica
# Validação: Média 58.7 anos (coerente com progressão natural)
```

### 4. Validação Estrutural e Integridade Pós-Tratamento

| Métrica | Antes | Depois | Status | Justificativa Técnica |
|---------|-------|--------|--------|----------------------|
| **Participantes** | 137 | 137 | ✅ Preservado | Nenhum participante removido - integridade amostral |
| **Colunas** | 125 | 133 | ✅ +8 derivadas | Variáveis calculadas seguindo padrões científicos |
| **IDs Únicos** | 137 | 137 | ✅ Preservado | Rastreabilidade individual mantida |
| **Missing Codes** | 888/999 | NaN | ✅ Padronizado | Tratamento estatístico correto para análises |
| **Outliers** | Presentes | Removidos | ✅ Corrigido | Valores biologicamente plausíveis estabelecidos |
| **Distribuições** | Distorcidas | Normalizadas | ✅ Validado | Testes de normalidade aplicados |
| **Correlações** | Mantidas | Preservadas | ✅ Confirmado | Estrutura de dependência entre variáveis intacta |
| **Integridade** | - | 100% | ✅ Validado | Validação cruzada completa realizada |

### Sistema de Roteamento Semântico

#### URLs Padronizadas para Dashboard 2 | Renomica-DAC
```python
/dashboard2/renomica-sociodemografico
/dashboard2/renomica-hipertensao
/dashboard2/renomica-diabetes
/dashboard2/renomica-tabagismo
/dashboard2/renomica-cardiaco
```

### Validação Cruzada Implementada

#### Testes de Consistência Aplicados
```python
# Validações automáticas implementadas
assert df['record_id'].nunique() == 137  # IDs únicos
assert df['idade'].between(18, 100).all()  # Idades plausíveis
assert df['imc'].between(15, 50).all()  # IMC biologicamente possível
assert (df['idade_diagnostico_hipertensao'] <= df['idade']).all()  # Consistência temporal
```

#### Métricas de Qualidade Monitoradas
- **Completude**: 95.2% dos dados preenchidos
- **Consistência**: Regras de negócio atendidas
- **Acurácia**: 0% de valores biologicamente impossíveis
- **Validade**: Variáveis dentro dos domínios esperados

## Requisitos Técnicos e Execução

### Instalação e Execução Detalhada

#### 1. Preparação do Ambiente de Desenvolvimento
```bash
# Clone do repositório oficial
git clone https://github.com/GenomasSUS/genoma_sus_fenotipos
cd genomasus-fenotipos

# Verificação da versão Python (requerido: 3.8+)
python --version

# Criação de ambiente virtual (altamente recomendado)
python -m venv venv

# Ativação do ambiente virtual
source venv/bin/activate  # Linux/Mac
# ou
venv\Scripts\activate     # Windows
```

#### 2. Instalação de Dependências
```bash
# Instalação via requirements.txt (recomendado)
pip install -r requirements.txt

# Ou instalação manual das dependências principais
pip install dash==2.14.0 plotly==5.15.0 pandas==2.0.0 numpy==1.24.0

# Verificação da instalação
python -c "import dash, plotly, pandas, numpy; print('Dependências instaladas com sucesso!')"
```

#### 3. Execução da Aplicação
```bash
# Execução padrão (produção)
python app.py

# Execução com debug habilitado (desenvolvimento)
python app.py --debug

# Execução com porta customizada
python app.py --port 8051

# Acesso via navegador
http://localhost:8050
```

### Conclusão do Trabalho Realizado

O **Dashboard Renomica-DAC** representa um passo importante em termos de visualização de dados fenotípicos no contexto do GenomaSUS Brasil, combinando rigor científico, qualidade técnica e experiência do usuário na interação com o dashboard de análise descritiva estatística. Todas as transformações foram validadas, documentadas e aprovadas juntamente com o time. Toda contribuição é bem-vinda!

#### Trabalho Realizado por Equipe Fenótipos

Última atualização realizada em 04/07/2025.
Autor(a): Nathalia Adriele.
