import pandas as pd
from dash import html, dcc, Dash, Input, Output, State
from components.navbar import create_navbar_for_route
from components.dynamic_sidebar import create_sidebar_for_route
from components.footer import footer
import plotly.express as px
import json
import os
from . import DATA_FILE

def get_current_palette():
    # Paletas de cores definidas
    COLOR_PALETTES = {
        "default": ["#6200EA", "#BB86FC", "#03DAC6", "#018786", "#FFEB3B", "#FFC107", "#FF9800", "#F44336", "#E91E63", "#9C27B0", "#673AB7", "#3F51B5"],
        "colorblind": ["#000000", "#E69F00", "#56B4E9", "#009E73", "#F0E442", "#0072B2", "#D55E00", "#CC79A7", "#999999", "#332288", "#44AA99", "#882255"],
        "autistic": ["#7F95D1", "#7FC9D1", "#7FD195", "#D1C97F", "#D17F95", "#957FD1", "#D1957F", "#95D17F", "#7F95D1", "#7FBCD1", "#D17F95", "#957FD1"],
        "monochrome": ["#0D47A1", "#1565C0", "#1976D2", "#1E88E5", "#2196F3", "#42A5F5", "#64B5F6", "#90CAF9", "#BBDEFB", "#E3F2FD", "#2962FF", "#0D47A1"],
        "pastel": ["#FFB6C1", "#FFD700", "#98FB98", "#ADD8E6", "#FFA07A", "#DDA0DD", "#87CEFA", "#F0E68C", "#E6E6FA", "#FFDAB9", "#B0E0E6", "#FFDEAD"]
    }
    
    config_file = 'config/app_config.json'
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        palette_key = config.get('color_palette', 'default')
    else:
        palette_key = 'default'
    
    return COLOR_PALETTES.get(palette_key, COLOR_PALETTES['default'])

df = pd.read_csv(DATA_FILE)

df['idade'] = 2010 - df['Ano1aa_nasc']
df = df[df['idade'].between(0, 110)]

def create_graph(title, figure, palette_colors=None):
    if palette_colors is None:
        palette_colors = get_current_palette()
    
    if 'data' in figure and figure['data']:
        for i, trace in enumerate(figure['data']):
            if 'marker' in trace:
                if 'color' in trace['marker'] and isinstance(trace['marker']['color'], str):
                    color_index = i % len(palette_colors)
                    trace['marker']['color'] = palette_colors[color_index]
                elif 'colors' in trace['marker'] and isinstance(trace['marker']['colors'], list):
                    num_colors = len(trace['marker']['colors'])
                    trace['marker']['colors'] = palette_colors[:num_colors]
    
    return html.Div([
        html.H3(title, style={'textAlign': 'center'}),
        dcc.Graph(figure=figure)
    ], className='graph-container')

layout = html.Div(
    className='page-container',
    children=[
        html.Link(
            rel='stylesheet',
            href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
        ),
        
        create_navbar_for_route("/dashboard1/sabe-sociodemografico"),
        html.Div(
            className='main-wrapper',
            id='main-wrapper',
            children=[
                create_sidebar_for_route("/dashboard1/sabe-sociodemografico"),
                html.Div(
                    className='content',
                    children=[
                        html.Div(id='dashboard1-content'),
                    ],
                ),
            ],
        ),
        footer,
    ],
)

# Filtros globais.
filters = html.Div([
    html.Label("Filtrar por Sexo:", style={'marginRight': '10px'}),
    dcc.Dropdown(
        id='sexo-filter',
        options=[{'label': 'Todos', 'value': 'all'}] + 
                [{'label': 'Masculino' if x == False else 'Feminino', 'value': x} for x in [False, True]],
        value='all',
        clearable=False,
        style={'width': '200px'}
    ),
    html.Label("Filtrar por Raça/Cor:", style={'marginLeft': '20px', 'marginRight': '10px'}),
    dcc.Dropdown(
        id='raca-filter',
        options=[{'label': 'Todos', 'value': 'all'}] + 
                [{'label': col.replace('A12_raca_', '').capitalize(), 'value': col} for col in df.columns if 'A12_raca_' in col],
        value='all',
        clearable=False,
        style={'width': '200px'}
    )
], style={'display': 'flex', 'justifyContent': 'center', 'marginBottom': '20px'})

graphs = html.Div(id='dynamic-graphs')

dashboard1_main_content = html.Div([
    html.H1("Estatísticas Sociodemográficas - SABE (GS-009)", style={'textAlign': 'center'}),
    filters,
    graphs
])

# Callbacks
def register_callbacks(app):
    @app.callback(
        Output('dynamic-graphs', 'children'),
        [Input('sexo-filter', 'value'), Input('raca-filter', 'value')]
    )
    def update_graphs(sexo_filter, raca_filter):
        palette_colors = get_current_palette()
        
        filtered_df = df.copy()
        if sexo_filter != 'all':
            filtered_df = filtered_df[filtered_df['C18_entrevistado_sex_fem'] == sexo_filter]
        
        if raca_filter != 'all':
            filtered_df = filtered_df[filtered_df[raca_filter] == 1]

        graph_1 = create_graph("Distribuição Etária", {
            'data': [{'x': filtered_df['idade'], 'type': 'histogram', 'marker': {'color': palette_colors[0]}}],
            'layout': {'title': 'Distribuição Etária'}
        }, palette_colors)

        graph_2 = create_graph("Distribuição por Sexo", {
            'data': [{
                'labels': ['Feminino', 'Masculino'],
                'values': filtered_df['C18_entrevistado_sex_fem'].value_counts(),
                'type': 'pie',
                'marker': {'colors': [palette_colors[1], palette_colors[2]]}
            }],
            'layout': {'title': 'Distribuição por Sexo'}
        }, palette_colors)

        graph_3 = create_graph("Distribuição Etária por Sexo", {
            'data': [
                {'x': filtered_df[filtered_df['C18_entrevistado_sex_fem'] == False]['idade'], 'name': 'Masculino', 'type': 'histogram', 'marker': {'color': palette_colors[2]}},
                {'x': filtered_df[filtered_df['C18_entrevistado_sex_fem'] == True]['idade'], 'name': 'Feminino', 'type': 'histogram', 'marker': {'color': palette_colors[1]}}
            ],
            'layout': {'barmode': 'stack', 'title': 'Distribuição Etária por Sexo'}
        }, palette_colors)

        graph_4 = create_graph("Distribuição de Renda", {
            'data': [{'x': filtered_df['H26c_receita_em_reais'], 'type': 'histogram', 'marker': {'color': palette_colors[3]}}],
            'layout': {'title': 'Distribuição de Renda'}
        }, palette_colors)

        graph_5 = create_graph("Distribuição de Renda por Sexo", {
            'data': [
                {'x': filtered_df[filtered_df['C18_entrevistado_sex_fem'] == False]['H26c_receita_em_reais'], 'name': 'Masculino', 'type': 'box', 'marker': {'color': palette_colors[2]}},
                {'x': filtered_df[filtered_df['C18_entrevistado_sex_fem'] == True]['H26c_receita_em_reais'], 'name': 'Feminino', 'type': 'box', 'marker': {'color': palette_colors[1]}}
            ],
            'layout': {'title': 'Distribuição de Renda por Sexo'}
        }, palette_colors)

        raca_cor_data = []
        for i, col in enumerate([c for c in filtered_df.columns if 'A12_raca_' in c]):
            color_index = (i % len(palette_colors))  
            raca_cor_data.append({
                'x': filtered_df[filtered_df[col] == 1]['H26c_receita_em_reais'],
                'name': col.replace('A12_raca_', '').capitalize(),
                'type': 'box',
                'marker': {'color': palette_colors[color_index]}
            })
        graph_6 = create_graph("Distribuição de Renda por Raça/Cor", {
            'data': raca_cor_data,
            'layout': {'title': 'Distribuição de Renda por Raça/Cor'}
        }, palette_colors)

        graph_7 = create_graph("Distribuição de Raça/Cor", {
            'data': [{
                'labels': ['Amarelo', 'Branco', 'Indígena', 'Não Respondeu', 'Não Sei', 'Outra', 'Pardo', 'Preto'],
                'values': filtered_df[['A12_raca_amarelo', 'A12_raca_branco', 'A12_raca_indigena', 'A12_raca_nr', 'A12_raca_ns', 'A12_raca_outra', 'A12_raca_pardo', 'A12_raca_preto']].sum(),
                'type': 'pie',
                'marker': {'colors': palette_colors[:8]}  # Usar apenas as primeiras 8 cores da paleta
            }],
            'layout': {'title': 'Distribuição de Raça/Cor'}
        }, palette_colors)
        
        race_gender_data = []
        race_columns = [c for c in filtered_df.columns if 'A12_raca_' in c]

        for i, race_col in enumerate(race_columns):
            race_name = race_col.replace("A12_raca_", "").capitalize()
            
            male_count = len(filtered_df[(filtered_df['C18_entrevistado_sex_fem'] == False) & (filtered_df[race_col] == 1)])
            female_count = len(filtered_df[(filtered_df['C18_entrevistado_sex_fem'] == True) & (filtered_df[race_col] == 1)])
            
            race_gender_data.append({
                'x': [race_name],
                'y': [male_count],
                'name': 'Masculino',
                'type': 'bar',
                'marker': {'color': palette_colors[2]}
            })
            
            race_gender_data.append({
                'x': [race_name],
                'y': [female_count],
                'name': 'Feminino',
                'type': 'bar',
                'marker': {'color': palette_colors[1]}
            })
        
        graph_8 = create_graph("Distribuição de Raça/Cor por Sexo", {
            'data': race_gender_data,
            'layout': {
                'barmode': 'group',
                'title': 'Distribuição de Raça/Cor por Sexo',
                'xaxis': {'title': 'Raça/Cor'},
                'yaxis': {'title': 'Número de pessoas'},
                'legend': {'orientation': 'h', 'y': -0.2},
                'margin': {'t': 50, 'b': 100},
                'height': 500
            }
        })

        graph_9 = create_graph("Distribuição de Escolaridade", {
            'data': [{
                'labels': ['1º Grau', '2º Grau', 'Graduação', 'Mestrado', 'Não Se Aplica', 'Pós-Graduação', 'Supletivo', 'Técnico'],
                'values': filtered_df[['A06n_edu_1_grau', 'A06n_edu_2_grau', 'A06n_edu_gradu', 'A06n_edu_magist', 'A06n_edu_ns', 'A06n_edu_pos_gradu', 'A06n_edu_supletivo', 'A06n_edu_tecnico']].sum(),
                'type': 'pie',
                'marker': {'colors': ['#FFEB3B', '#FFC107', '#FF9800', '#F44336', '#E91E63', '#9C27B0', '#673AB7', '#3F51B5']}
            }],
            'layout': {'title': 'Distribuição de Escolaridade'}
        })

        education_columns = [c for c in filtered_df.columns if 'A06n_edu_' in c]
        
        education_gender_data = []
        
        edu_colors_male = ['#1f77b4', '#2ca02c', '#9467bd', '#d62728', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22']
        edu_colors_female = ['#17becf', '#98df8a', '#c5b0d5', '#ff9896', '#c49c94', '#f7b6d2', '#c7c7c7', '#dbdb8d']
        
        for i, col in enumerate(education_columns):
            education_name = col.replace("A06n_edu_", "").capitalize()
            
            male_count = len(filtered_df[(filtered_df['C18_entrevistado_sex_fem'] == False) & (filtered_df[col] == 1)])
            female_count = len(filtered_df[(filtered_df['C18_entrevistado_sex_fem'] == True) & (filtered_df[col] == 1)])
            
            if male_count > 0:
                education_gender_data.append({
                    'x': [education_name], 
                    'y': [male_count],
                    'name': f'Masculino - {education_name}',
                    'type': 'bar',
                    'marker': {'color': edu_colors_male[i % len(edu_colors_male)]},
                    'legendgroup': 'Masculino'
                })
            
            if female_count > 0:
                education_gender_data.append({
                    'x': [education_name], 
                    'y': [female_count],
                    'name': f'Feminino - {education_name}',
                    'type': 'bar',
                    'marker': {'color': edu_colors_female[i % len(edu_colors_female)]},
                    'legendgroup': 'Feminino'
                })
        
        graph_10 = create_graph("Distribuição de Escolaridade por Sexo", {
            'data': education_gender_data,
            'layout': {
                'barmode': 'group',
                'title': 'Distribuição de Escolaridade por Sexo',
                'xaxis': {'title': 'Nível de Escolaridade'},
                'yaxis': {'title': 'Número de pessoas'},
                'legend': {'orientation': 'h', 'y': -0.2},
                'margin': {'t': 50, 'b': 100},
                'height': 500
            }
        })

        edu_race_matrix = []
        
        race_columns = [c for c in filtered_df.columns if 'A12_raca_' in c]
        education_columns = [c for c in filtered_df.columns if 'A06n_edu_' in c]
        
        colorscale = [
            [0, 'rgb(247, 251, 255)'],
            [0.1, 'rgb(222, 235, 247)'],
            [0.2, 'rgb(198, 219, 239)'],
            [0.3, 'rgb(158, 202, 225)'],
            [0.4, 'rgb(107, 174, 214)'],
            [0.5, 'rgb(66, 146, 198)'],
            [0.6, 'rgb(33, 113, 181)'],
            [0.8, 'rgb(8, 81, 156)'],
            [1, 'rgb(8, 48, 107)']
        ]
        
        max_count = 0
        for race_col in race_columns:
            for edu_col in education_columns:
                count = len(filtered_df[(filtered_df[race_col] == 1) & (filtered_df[edu_col] == 1)])
                if count > max_count:
                    max_count = count
        
        x_labels = [col.replace("A12_raca_", "").capitalize() for col in race_columns]
        y_labels = [col.replace("A06n_edu_", "").capitalize() for col in education_columns]
        
        z_values = []
        for edu_col in education_columns:
            row = []
            for race_col in race_columns:
                count = len(filtered_df[(filtered_df[race_col] == 1) & (filtered_df[edu_col] == 1)])
                row.append(count)
            z_values.append(row)
        
        edu_race_matrix = [{
            'z': z_values,
            'x': x_labels,
            'y': y_labels,
            'type': 'heatmap',
            'colorscale': colorscale,
            'showscale': True,
            'colorbar': {'title': 'Número de pessoas'},
            'hovertemplate': 'Raça/Cor: %{x}<br>Escolaridade: %{y}<br>Número: %{z}<extra></extra>'
        }]
        
        graph_11 = create_graph("Distribuição de Escolaridade por Raça/Cor", {
            'data': edu_race_matrix,
            'layout': {
                'title': 'Distribuição de Escolaridade por Raça/Cor',
                'xaxis': {'title': 'Raça/Cor'},
                'yaxis': {'title': 'Nível de Escolaridade'},
                'margin': {'t': 50, 'b': 50, 'l': 140},
                'height': 600
            }
        })

        escolaridade_renda_data = []
        for i, col in enumerate([c for c in filtered_df.columns if 'A06n_edu_' in c]):
            escolaridade_renda_data.append({
                'x': filtered_df[filtered_df[col] == 1]['H26c_receita_em_reais'],
                'name': col.replace('A06n_edu_', '').capitalize(),
                'type': 'box',
                'marker': {'color': f'rgb({i*30}, {i*20}, {i*10})'}
            })
        graph_12 = create_graph("Distribuição de Escolaridade por Renda", {
            'data': escolaridade_renda_data,
            'layout': {'title': 'Distribuição de Escolaridade por Renda'}
        })

        return html.Div([
            html.Div([graph_1, graph_2], className='row'),
            html.Div([graph_3, graph_4], className='row'),
            html.Div([graph_5, graph_6], className='row'),
            html.Div([graph_7, graph_8], className='row'),
            html.Div([graph_9, graph_10], className='row'),
            html.Div([graph_11, graph_12], className='row')
        ])

    @app.callback(
        Output('dashboard1-content', 'children'),
        [Input('url', 'pathname')]
    )
    def display_dashboard1_content(pathname):
        if pathname == '/dashboard1':
            return dashboard1_main_content
        elif pathname == '/dashboard1/subpage1':
            return html.H1("Estatísticas Hipertensão - SABE")
        elif pathname == '/dashboard1/subpage2':
            return html.H1("Estatísticas Diabetes - SABE")
        else:
            return dashboard1_main_content