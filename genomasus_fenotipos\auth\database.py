"""
Gerenciamento do banco de dados SQLite para autenticação
GenomaSUS Fenótipos - Sistema de Login
"""

import sqlite3
import os
from datetime import datetime
from .auth_utils import hash_password

DB_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'auth_database.db')

def init_database():
    """
    Inicializa o banco de dados SQLite e cria a tabela users
    Insere o usuário de teste se não existir
    """
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP
            )
        ''')
        
        cursor.execute('SELECT COUNT(*) FROM users WHERE username = ?', ('nathalia.adriele',))
        user_exists = cursor.fetchone()[0] > 0
        
        if not user_exists:
            test_password_hash = hash_password('adriele')
            cursor.execute('''
                INSERT INTO users (username, password_hash, created_at)
                VALUES (?, ?, ?)
            ''', ('nathalia.adriele', test_password_hash, datetime.now()))
            
            print("✅ Usuário de teste criado: nathalia.adriele / adriele")
        
        conn.commit()
        conn.close()
        print("✅ Banco de dados inicializado com sucesso!")
        return True
        
    except Exception as e:
        print(f"❌ Erro ao inicializar banco de dados: {e}")
        return False

def authenticate_user(username, password):
    """
    Autentica um usuário verificando credenciais no banco
    
    Args:
        username (str): Nome de usuário
        password (str): Senha em texto plano
        
    Returns:
        dict: Dados do usuário se autenticado, None caso contrário
    """
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, password_hash, created_at, last_login 
            FROM users WHERE username = ?
        ''', (username,))
        
        user_data = cursor.fetchone()
        
        if user_data:
            user_id, db_username, password_hash, created_at, last_login = user_data
            
            from .auth_utils import verify_password
            if verify_password(password, password_hash):
                cursor.execute('''
                    UPDATE users SET last_login = ? WHERE id = ?
                ''', (datetime.now(), user_id))
                conn.commit()
                
                conn.close()
                return {
                    'id': user_id,
                    'username': db_username,
                    'created_at': created_at,
                    'last_login': datetime.now().isoformat()
                }
        
        conn.close()
        return None
        
    except Exception as e:
        print(f"❌ Erro na autenticação: {e}")
        return None

def get_user_count():
    """
    Retorna o número total de usuários cadastrados
    """
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM users')
        count = cursor.fetchone()[0]
        conn.close()
        return count
    except:
        return 0
