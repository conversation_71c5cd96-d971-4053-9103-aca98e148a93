from dash import html, dcc
import dash_bootstrap_components as dbc
import sys
import os

# Adicionar o diretório raiz ao path para importar config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.dashboard_colors import DASHBOARD_COLORS

# Função para converter hex para RGB
def hex_to_rgb(hex_color):
    """Converte cor hexadecimal para RGB"""
    hex_color = hex_color.lstrip('#')
    return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

# Função para criar cards
def create_dashboard_card(title, icon, color, href, description):
    return dcc.Link(
        href=href,
        style={'text-decoration': 'none'},
        children=html.Div(
            style={
                'width': '320px',
                'min-width': '300px',
                'height': '220px',
                'background-color': '#ffffff',
                'border-radius': '12px',
                'box-shadow': '0 6px 12px rgba(0, 0, 0, 0.1)',
                'display': 'flex',
                'flex-direction': 'column',
                'align-items': 'center',
                'justify-content': 'center',
                'text-align': 'center',
                'padding': '20px',
                'color': '#333',
                'transition': 'transform 0.3s ease',
                'position': 'relative',
                'overflow': 'hidden',
                'cursor': 'pointer',
                'flex-shrink': '0',
            },
            className='dashboard-card',
            children=[
                html.Div(
                    style={
                        'position': 'absolute',
                        'top': '0',
                        'left': '0',
                        'right': '0',
                        'height': '6px',
                        'background-color': color,
                    }
                ),
                html.Div(
                    style={
                        'background-color': f'rgba{hex_to_rgb(color) + (0.15,)}',
                        'width': '72px',
                        'height': '72px',
                        'border-radius': '50%',
                        'display': 'flex',
                        'align-items': 'center',
                        'justify-content': 'center',
                        'margin-bottom': '15px',
                    },
                    children=html.I(
                        className=f'fa {icon}',
                        style={
                            'font-size': '32px',
                            'color': color,
                            'text-align': 'center',
                            'line-height': '1',
                        }
                    ),
                ),
                html.H3(
                    children=title,
                    style={
                        'margin': '10px 0',
                        'font-size': '22px',
                        'font-weight': '600',
                    }
                ),
                html.P(
                    children=description,
                    style={
                        'margin': '0',
                        'font-size': '14px',
                        'color': '#666',
                        'line-height': '1.4',
                    }
                ),
            ],
        ),
    )

# Layout principal usando Bootstrap
layout = dbc.Container([
    # Cabeçalho Principal
    dbc.Row([
        dbc.Col([
            html.H2(
                "Visualize os Painéis Analíticos do Projeto GenomaSUS – Fenótipos",
                style={
                    'color': '#333',
                    'font-weight': 'bold',
                    'text-align': 'center',
                    'margin-bottom': '15px',
                    'font-size': '28px'
                }
            ),
            html.P(
                "Explore estatísticas descritivas e ferramentas integrativas dos estudos participantes.",
                style={
                    'color': '#666',
                    'text-align': 'center',
                    'font-size': '16px',
                    'margin-bottom': '40px'
                }
            )
        ], width=12)
    ], className="mb-4"),
    # SEÇÃO 1 – ESTUDOS PARTICIPANTES
    dbc.Row([
        dbc.Col([
            html.H4(
                "Estudos Participantes",
                style={
                    'color': '#2c3e50',
                    'font-weight': '600',
                    'text-align': 'center',
                    'margin-top': '40px',
                    'margin-bottom': '15px',
                    'font-size': '22px'
                }
            ),
            html.P(
                "Análises de estatística descritiva dos bancos de dados dos estudos genômicos",
                style={
                    'color': '#888',
                    'font-size': '15px',
                    'text-align': 'center',
                    'margin-bottom': '20px'
                }
            )
        ], width=12)
    ]),

    # Cards dos estudos participantes em linha
    html.Div([
        html.Div([
            create_dashboard_card(
                title=DASHBOARD_COLORS["dashboard1"]["name"],
                icon="fa-chart-bar",
                color=DASHBOARD_COLORS["dashboard1"]["color"],
                href=DASHBOARD_COLORS["dashboard1"]["routes"][0],
                description="Análises de estatística descritiva do banco SABE (Saúde, Bem-estar e Envelhecimento)."
            ),
            create_dashboard_card(
                title=DASHBOARD_COLORS["dashboard2"]["name"],
                icon="fa-chart-line",
                color=DASHBOARD_COLORS["dashboard2"]["color"],
                href=DASHBOARD_COLORS["dashboard2"]["routes"][0],
                description="Análises de estatística descritiva do banco Renomica-DAC (Rede Nacional de Genômica Cardiovascular – Doença Arterial Coronariana)."
            ),
            create_dashboard_card(
                title=DASHBOARD_COLORS["dashboard3"]["name"],
                icon="fa-dna",
                color=DASHBOARD_COLORS["dashboard3"]["color"],
                href=DASHBOARD_COLORS["dashboard3"]["routes"][0],
                description="Análises de estatística descritiva do banco Farmacogenética (EPIGEN-II-PGx-cardiovascular)."
            )
        ],
        className="cards-container",
        style={
            'display': 'flex',
            'flex-wrap': 'wrap',
            'justify-content': 'center',
            'gap': '35px',
            'margin-bottom': '40px',
            'max-width': '1350px',
            'margin-left': 'auto',
            'margin-right': 'auto'
        })
    ]),

    # Separador visual entre seções
    html.Hr(style={
        'margin': '60px auto 20px auto',
        'width': '70%',
        'border': 'none',
        'height': '1px',
        'background-color': '#dee2e6'
    }),

    # SEÇÃO 2 – BASE DE CONHECIMENTO E INTEGRAÇÃO
    dbc.Row([
        dbc.Col([
            html.H4(
                "Base de Conhecimento e Integração",
                style={
                    'color': '#2c3e50',
                    'font-weight': '600',
                    'text-align': 'center',
                    'margin-top': '40px',
                    'margin-bottom': '15px',
                    'font-size': '22px'
                }
            ),
            html.P(
                "Ferramentas transversais para harmonização, qualidade e integração de dados",
                style={
                    'color': '#777',
                    'font-size': '15px',
                    'text-align': 'center',
                    'margin-bottom': '20px'
                }
            )
        ], width=12)
    ]),

    # Cards de Base de Conhecimento e Integração em linha
    html.Div([
        html.Div([
            create_dashboard_card(
                title="Harmonização de Variáveis",
                icon="fa-layer-group",  # Ícone representativo de camadas integradas/harmonização
                color="#F9E79F",  # Amarelo claro suave
                href="/harmonizacao/comparativo",
                description="Comparação de variáveis harmonizadas entre os estudos."
            ),
            create_dashboard_card(
                title="Documentações e Repositório GitHub",
                icon="fa-book-open",  # Ícone de livro aberto
                color="#FFB347",  # Laranja claro
                href="/repositorio",
                description="Acesso às documentações técnicas e ao repositório oficial do projeto no GitHub."
            ),
            create_dashboard_card(
                title="Métricas de Qualidade",
                icon="fa-chart-line",  # Ícone de gráfico
                color="#FF9999",  # Vermelho claro
                href="/qualidade-dados",
                description="Visualização de métricas relacionadas à completude, consistência e qualidade dos dados."
            )
        ],
        className="cards-container",
        style={
            'display': 'flex',
            'flex-wrap': 'wrap',
            'justify-content': 'center',
            'gap': '35px',
            'margin-bottom': '40px',
            'max-width': '1350px',
            'margin-left': 'auto',
            'margin-right': 'auto'
        })
    ])

],
fluid=True,
style={
    'background-color': '#f8f9fa',
    'min-height': '100vh',
    'padding': '30px 16px',
    'max-width': '92%',
    'margin': '0 auto'
},
className="responsive-container")
