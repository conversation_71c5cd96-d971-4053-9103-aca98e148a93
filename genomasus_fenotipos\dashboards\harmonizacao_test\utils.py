#!/usr/bin/env python3
"""
Utilitários para harmonização de variáveis entre estudos
Funções para carregamento e processamento de dados dos estudos GS-009, GS-004 e GS-032
"""

import pandas as pd
import numpy as np
import os
from typing import Dict, List, Tuple, Optional

# Caminhos dos arquivos de dados
DATA_DIR = os.path.join(os.path.dirname(__file__), '..', 'data')
HARMONIZATION_FILE = os.path.join(DATA_DIR, 'Variaveis-Comuns-GS009-GS004-GS032.xlsx - Perguntas-Variaveis-Corresponde.csv')
GS009_FILE = os.path.join(DATA_DIR, 'banco_genoma_2010_final_dash.csv')
GS004_FILE = os.path.join(DATA_DIR, 'banco_renomicadac_processado_2025_final_dash.csv')
GS032_FILE = os.path.join(DATA_DIR, 'banco_processado_farmacogenetica_final_dash.csv')

def load_harmonization_mapping() -> pd.DataFrame:
    """
    Carrega o arquivo de mapeamento de variáveis harmonizadas
    
    Returns:
        pd.DataFrame: DataFrame com o mapeamento das variáveis
    """
    try:
        df = pd.read_csv(HARMONIZATION_FILE, encoding='utf-8')
        return df
    except UnicodeDecodeError:
        # Tentar com encoding latin-1 se utf-8 falhar
        df = pd.read_csv(HARMONIZATION_FILE, encoding='latin-1')
        return df

def load_study_data(study: str) -> pd.DataFrame:
    """
    Carrega os dados de um estudo específico
    
    Args:
        study (str): Nome do estudo ('GS-009', 'GS-004', 'GS-032')
    
    Returns:
        pd.DataFrame: DataFrame com os dados do estudo
    """
    file_mapping = {
        'GS-009': GS009_FILE,
        'GS-004': GS004_FILE,
        'GS-032': GS032_FILE
    }
    
    if study not in file_mapping:
        raise ValueError(f"Estudo {study} não encontrado. Estudos disponíveis: {list(file_mapping.keys())}")
    
    try:
        df = pd.read_csv(file_mapping[study], encoding='utf-8')
        return df
    except UnicodeDecodeError:
        df = pd.read_csv(file_mapping[study], encoding='latin-1')
        return df

def get_harmonized_questions() -> List[str]:
    """
    Retorna a lista das 6 perguntas harmonizadas principais
    
    Returns:
        List[str]: Lista das perguntas harmonizadas
    """
    return [
        "Qual a distribuição etária dos participantes?",
        "Qual a distribuição do sexo dos participantes?",
        "Qual a distribuição de raça/cor dos participantes?",
        "Qual a distribuição de renda dos participantes?",
        "Qual a prevalência da hipertensão no estudo?",
        "Qual a prevalência da diabetes no estudo?"
    ]

def parse_variables_for_study(variables_str: str) -> List[str]:
    """
    Converte string de variáveis em lista, tratando quebras de linha
    
    Args:
        variables_str (str): String com variáveis separadas por quebra de linha
    
    Returns:
        List[str]: Lista de variáveis
    """
    if pd.isna(variables_str) or variables_str == 'NA':
        return []
    
    # Dividir por quebras de linha e limpar espaços
    variables = [var.strip() for var in str(variables_str).split('\n') if var.strip()]
    return variables

def get_variables_for_question(question: str) -> Dict[str, List[str]]:
    """
    Retorna as variáveis correspondentes para cada estudo para uma pergunta específica
    
    Args:
        question (str): Pergunta harmonizada
    
    Returns:
        Dict[str, List[str]]: Dicionário com variáveis por estudo
    """
    mapping_df = load_harmonization_mapping()
    
    # Filtrar pela pergunta
    question_row = mapping_df[mapping_df['Pergunta'] == question]
    
    if question_row.empty:
        return {'GS-009': [], 'GS-004': [], 'GS-032': []}
    
    row = question_row.iloc[0]
    
    return {
        'GS-009': parse_variables_for_study(row['GS-009']),
        'GS-004': parse_variables_for_study(row['GS-004']),
        'GS-032': parse_variables_for_study(row['GS-032'])
    }

def calculate_age_from_birth_year(birth_year: pd.Series, reference_year: int = 2010) -> pd.Series:
    """
    Calcula idade a partir do ano de nascimento
    
    Args:
        birth_year (pd.Series): Série com anos de nascimento
        reference_year (int): Ano de referência para cálculo
    
    Returns:
        pd.Series: Série com idades calculadas
    """
    return reference_year - birth_year

def categorize_age(age: pd.Series) -> pd.Series:
    """
    Categoriza idades em faixas etárias
    
    Args:
        age (pd.Series): Série com idades
    
    Returns:
        pd.Series: Série com categorias etárias
    """
    bins = [0, 30, 40, 50, 60, 70, 80, 100]
    labels = ['< 30', '30-39', '40-49', '50-59', '60-69', '70-79', '≥ 80']
    return pd.cut(age, bins=bins, labels=labels, right=False)

def standardize_sex_variable(data: pd.DataFrame, variables: List[str]) -> pd.Series:
    """
    Padroniza variáveis de sexo para formato comum

    Args:
        data (pd.DataFrame): DataFrame com os dados
        variables (List[str]): Lista de variáveis de sexo

    Returns:
        pd.Series: Série padronizada (Feminino/Masculino)
    """
    if not variables:
        return pd.Series(dtype='object')

    # Para GS-009: C18_entrevistado_sex_fem (True/False)
    if 'C18_entrevistado_sex_fem' in variables and 'C18_entrevistado_sex_fem' in data.columns:
        return data['C18_entrevistado_sex_fem'].map({True: 'Feminino', False: 'Masculino'})

    # Para GS-032: sex_feminino (True/False)
    if 'sex_feminino' in variables and 'sex_feminino' in data.columns:
        return data['sex_feminino'].map({True: 'Feminino', False: 'Masculino'})

    # Para GS-004: verificar se existe a variável categórica direta
    if 'qual_o_seu_sexo_biologico' in data.columns:
        return data['qual_o_seu_sexo_biologico'].copy()

    # Para GS-004: múltiplas variáveis binárias (caso existam)
    if any('feminino' in var for var in variables):
        fem_var = next((var for var in variables if 'feminino' in var), None)
        masc_var = next((var for var in variables if 'masculino' in var), None)

        if fem_var in data.columns and masc_var in data.columns:
            result = pd.Series(index=data.index, dtype='object')
            result[data[fem_var] == True] = 'Feminino'
            result[data[masc_var] == True] = 'Masculino'
            return result

    return pd.Series(dtype='object')

def process_race_variables(data: pd.DataFrame, variables: List[str]) -> pd.Series:
    """
    Processa variáveis de raça/cor para formato padronizado
    
    Args:
        data (pd.DataFrame): DataFrame com os dados
        variables (List[str]): Lista de variáveis de raça
    
    Returns:
        pd.Series: Série com categorias de raça/cor padronizadas
    """
    if not variables:
        return pd.Series(dtype='object')
    
    # Mapeamento de categorias
    race_mapping = {
        'branco': 'Branca',
        'white': 'Branca',
        'pardo': 'Parda',
        'preto': 'Preta',
        'black': 'Preta',
        'amarelo': 'Amarela',
        'yellow': 'Amarela',
        'indigena': 'Indígena',
        'indigenous': 'Indígena'
    }
    
    result = pd.Series(index=data.index, dtype='object')
    
    for var in variables:
        if var in data.columns:
            # Para variáveis binárias (GS-009, GS-032)
            if data[var].dtype == bool or data[var].isin([True, False, 0, 1]).all():
                for race_key, race_value in race_mapping.items():
                    if race_key in var.lower():
                        result[data[var] == True] = race_value
                        break
            # Para variáveis categóricas (GS-004)
            else:
                # Mapear valores categóricos diretamente
                mapped_values = data[var].str.lower().map(race_mapping)
                # Preencher apenas valores não nulos
                result[mapped_values.notna()] = mapped_values[mapped_values.notna()]
    
    return result

def process_income_variables(data: pd.DataFrame, variables: List[str]) -> pd.Series:
    """
    Processa variáveis de renda para categorias padronizadas

    Args:
        data (pd.DataFrame): DataFrame com os dados
        variables (List[str]): Lista de variáveis de renda

    Returns:
        pd.Series: Série com categorias de renda padronizadas
    """
    if not variables:
        return pd.Series(dtype='object')

    result = pd.Series(index=data.index, dtype='object')

    for var in variables:
        if var in data.columns:
            # Para GS-009: H26c_receita_em_reais (valores numéricos)
            if var == 'H26c_receita_em_reais':
                income_values = pd.to_numeric(data[var], errors='coerce')
                # Categorizar por salários mínimos (assumindo SM = R$ 510 em 2010)
                bins = [0, 510, 1020, 2550, 5100, float('inf')]
                labels = ['Até 1 SM', '1-2 SM', '2-5 SM', '5-10 SM', 'Mais de 10 SM']
                result = pd.cut(income_values, bins=bins, labels=labels, right=False)

            # Para GS-032: family_income_category (harmonizar categorias em inglês)
            elif var == 'family_income_category':
                # Mapear categorias em inglês para português padronizado
                income_mapping = {
                    'less_than_1_salary': 'Até 1 SM',
                    'up_to_2_salaries': '1-2 SM',
                    '2_to_5_salaries': '2-5 SM',
                    'more_than_5_salaries': 'Mais de 5 SM',
                    'missing': None  # Excluir valores missing
                }
                result = data[var].map(income_mapping)

    return result

def process_hypertension_variables(data: pd.DataFrame, variables: List[str]) -> pd.Series:
    """
    Processa variáveis de hipertensão para formato padronizado

    Args:
        data (pd.DataFrame): DataFrame com os dados
        variables (List[str]): Lista de variáveis de hipertensão

    Returns:
        pd.Series: Série padronizada (Sim/Não)
    """
    if not variables:
        return pd.Series(dtype='object')

    for var in variables:
        if var in data.columns:
            # Verificar se é numérica 0/1 ou booleana
            unique_values = set(data[var].dropna().unique())
            # Converter numpy types para tipos Python padrão para comparação
            unique_values_converted = {int(x) if hasattr(x, 'item') else x for x in unique_values}

            if unique_values_converted.issubset({0, 1}) or unique_values_converted.issubset({True, False}):
                # Converter para int antes do mapeamento para garantir compatibilidade
                return data[var].astype(int).map({1: 'Sim', 0: 'Não'})
            # Variáveis categóricas
            else:
                # Mapear valores comuns
                mapping = {
                    'sim': 'Sim', 'Sim': 'Sim', 'SIM': 'Sim', 'yes': 'Sim', 'Yes': 'Sim',
                    'não': 'Não', 'Não': 'Não', 'NÃO': 'Não', 'nao': 'Não', 'no': 'Não', 'No': 'Não'
                }
                mapped_result = data[var].map(mapping)
                if not mapped_result.isna().all():  # Se conseguiu mapear algum valor
                    return mapped_result

    return pd.Series(dtype='object')

def process_diabetes_variables(data: pd.DataFrame, variables: List[str]) -> pd.Series:
    """
    Processa variáveis de diabetes para formato padronizado

    Args:
        data (pd.DataFrame): DataFrame com os dados
        variables (List[str]): Lista de variáveis de diabetes

    Returns:
        pd.Series: Série padronizada (Sim/Não)
    """
    if not variables:
        return pd.Series(dtype='object')

    result = pd.Series(index=data.index, dtype='object')

    for var in variables:
        if var in data.columns:
            # Para variáveis booleanas simples ou numéricas (0/1)
            if var in ['C05_tem_diabetes', 'diagnostico_diabetes']:
                unique_values = set(data[var].dropna().unique())
                # Converter numpy types para tipos Python padrão para comparação
                unique_values_converted = {int(x) if hasattr(x, 'item') else x for x in unique_values}

                if unique_values_converted.issubset({0, 1}) or unique_values_converted.issubset({True, False}):
                    # Converter para int antes do mapeamento para garantir compatibilidade
                    return data[var].astype(int).map({1: 'Sim', 0: 'Não'})

            # Para GS-032: múltiplas variáveis de tipo de diabetes
            elif 'diabetes_type' in var:
                if 'no_diabetes' in var:
                    result[data[var] == True] = 'Não'
                elif any(diabetes_type in var for diabetes_type in ['type_1', 'type_2']):
                    result[data[var] == True] = 'Sim'

    return result

def get_harmonized_data_for_question(question: str) -> Dict[str, pd.DataFrame]:
    """
    Retorna dados harmonizados para uma pergunta específica

    Args:
        question (str): Pergunta harmonizada

    Returns:
        Dict[str, pd.DataFrame]: Dados processados por estudo
    """
    variables = get_variables_for_question(question)
    studies = ['GS-009', 'GS-004', 'GS-032']

    harmonized_data = {}

    for study in studies:
        try:
            data = load_study_data(study)
            study_vars = variables[study]

            if not study_vars:
                harmonized_data[study] = pd.DataFrame()
                continue

            # Processar baseado no tipo de pergunta
            if "etária" in question:
                # Distribuição etária
                if study == 'GS-032':
                    # GS-032 já tem idade
                    if 'age_years' in study_vars and 'age_years' in data.columns:
                        age_data = data['age_years']
                        age_categories = categorize_age(age_data)
                        harmonized_data[study] = pd.DataFrame({
                            'categoria': age_categories,
                            'estudo': study
                        }).dropna()
                else:
                    # GS-009 e GS-004 têm ano de nascimento
                    birth_var = study_vars[0] if study_vars else None
                    if birth_var and birth_var in data.columns:
                        birth_years = pd.to_numeric(data[birth_var], errors='coerce')
                        ages = calculate_age_from_birth_year(birth_years)
                        age_categories = categorize_age(ages)
                        harmonized_data[study] = pd.DataFrame({
                            'categoria': age_categories,
                            'estudo': study
                        }).dropna()

            elif "sexo" in question:
                # Distribuição do sexo
                sex_data = standardize_sex_variable(data, study_vars)
                harmonized_data[study] = pd.DataFrame({
                    'categoria': sex_data,
                    'estudo': study
                }).dropna()

            elif "raça" in question or "cor" in question:
                # Distribuição de raça/cor
                race_data = process_race_variables(data, study_vars)
                harmonized_data[study] = pd.DataFrame({
                    'categoria': race_data,
                    'estudo': study
                }).dropna()

            elif "renda" in question:
                # Distribuição de renda
                income_data = process_income_variables(data, study_vars)
                harmonized_data[study] = pd.DataFrame({
                    'categoria': income_data,
                    'estudo': study
                }).dropna()

            elif "hipertensão" in question:
                # Prevalência de hipertensão
                hypertension_data = process_hypertension_variables(data, study_vars)
                harmonized_data[study] = pd.DataFrame({
                    'categoria': hypertension_data,
                    'estudo': study
                }).dropna()

            elif "diabetes" in question:
                # Prevalência de diabetes
                diabetes_data = process_diabetes_variables(data, study_vars)
                harmonized_data[study] = pd.DataFrame({
                    'categoria': diabetes_data,
                    'estudo': study
                }).dropna()

        except Exception as e:
            print(f"Erro ao processar {study} para '{question}': {e}")
            harmonized_data[study] = pd.DataFrame()

    return harmonized_data

def get_study_summary_stats() -> Dict[str, Dict[str, int]]:
    """
    Retorna estatísticas resumo dos três estudos

    Returns:
        Dict[str, Dict[str, int]]: Estatísticas por estudo
    """
    studies = ['GS-009', 'GS-004', 'GS-032']
    stats = {}

    for study in studies:
        try:
            data = load_study_data(study)
            stats[study] = {
                'total_participants': len(data),
                'columns_count': len(data.columns)
            }
        except Exception as e:
            stats[study] = {
                'total_participants': 0,
                'columns_count': 0,
                'error': str(e)
            }

    return stats
