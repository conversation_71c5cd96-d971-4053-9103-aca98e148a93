import pandas as pd
from dash import html, dcc, Input, Output
from components.navbar import create_navbar_for_route
from components.dynamic_sidebar import create_sidebar_for_route
from components.footer import footer
import json
import os
from . import DATA_FILE

def get_current_palette():
    # Paletas de cores definidas
    COLOR_PALETTES = {
        "default": ["#6200EA", "#BB86FC", "#03DAC6", "#018786", "#FFEB3B", "#FFC107", "#FF9800", "#F44336", "#E91E63", "#9C27B0", "#673AB7", "#3F51B5"],
        "colorblind": ["#000000", "#E69F00", "#56B4E9", "#009E73", "#F0E442", "#0072B2", "#D55E00", "#CC79A7", "#999999", "#332288", "#44AA99", "#882255"],
        "autistic": ["#7F95D1", "#7FC9D1", "#7FD195", "#D1C97F", "#D17F95", "#957FD1", "#D1957F", "#95D17F", "#7F95D1", "#7FBCD1", "#D17F95", "#957FD1"],
        "monochrome": ["#0D47A1", "#1565C0", "#1976D2", "#1E88E5", "#2196F3", "#42A5F5", "#64B5F6", "#90CAF9", "#BBDEFB", "#E3F2FD", "#2962FF", "#0D47A1"],
        "pastel": ["#FFB6C1", "#FFD700", "#98FB98", "#ADD8E6", "#FFA07A", "#DDA0DD", "#87CEFA", "#F0E68C", "#E6E6FA", "#FFDAB9", "#B0E0E6", "#FFDEAD"]
    }
    
    config_file = 'config/app_config.json'
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        palette_key = config.get('color_palette', 'default')
    else:
        palette_key = 'default'
    
    return COLOR_PALETTES.get(palette_key, COLOR_PALETTES['default'])

# Carregamento e processamento dos dados
df = pd.read_csv(DATA_FILE)

def create_graph(title, figure, palette_colors=None):
    if palette_colors is None:
        palette_colors = get_current_palette()
    
    if 'data' in figure and figure['data']:
        for i, trace in enumerate(figure['data']):
            if 'marker' in trace:
                if 'color' in trace['marker'] and isinstance(trace['marker']['color'], str):
                    color_index = i % len(palette_colors)
                    trace['marker']['color'] = palette_colors[color_index]
                elif 'colors' in trace['marker'] and isinstance(trace['marker']['colors'], list):
                    num_colors = len(trace['marker']['colors'])
                    trace['marker']['colors'] = palette_colors[:num_colors]
    
    return html.Div([
        html.H3(title, style={'textAlign': 'center'}),
        dcc.Graph(figure=figure)
    ], className='graph-container')

# Layout da subpágina de Tabagismo e Alcoolismo
content = html.Div([
    html.H1("Estatísticas Tabagismo e Alcoolismo - Farmacogenética (GS-032)", style={'textAlign': 'center'}),
    
    # Filtros globais
    html.Div([
        html.Label("Filtrar por Sexo:", style={'marginRight': '10px'}),
        dcc.Dropdown(
            id='sexo-filter-subpage3-dashboard3',
            options=[{'label': 'Todos', 'value': 'all'}] + 
                    [{'label': 'Feminino', 'value': 1}, {'label': 'Masculino', 'value': 0}],
            value='all',
            clearable=False,
            style={'width': '200px'}
        ),
        html.Label("Filtrar por Raça/Cor:", style={'marginLeft': '20px', 'marginRight': '10px'}),
        dcc.Dropdown(
            id='raca-filter-subpage3-dashboard3',
            options=[{'label': 'Todos', 'value': 'all'}] +
                    [{'label': 'Branco', 'value': 'ethnicity_race_white'},
                     {'label': 'Pardo', 'value': 'ethnicity_race_pardo'},
                     {'label': 'Preto', 'value': 'ethnicity_race_black'},
                     {'label': 'Amarelo', 'value': 'ethnicity_race_yellow'},
                     {'label': 'Indígena', 'value': 'ethnicity_race_indigenous'}],
            value='all',
            clearable=False,
            style={'width': '200px'}
        )
    ], style={'display': 'flex', 'justifyContent': 'center', 'marginBottom': '20px'}),
    
    # Gráficos base (serão atualizados dinamicamente)
    html.Div(id='dynamic-graphs-subpage3-dashboard3')
], className='content')

# Layout principal com a estrutura comum
layout = html.Div(
    className='page-container',
    children=[
        create_navbar_for_route("/dashboard3/farmacogenetica-tabagismo-alcoolismo"),
        html.Div([
            create_sidebar_for_route("/dashboard3/farmacogenetica-tabagismo-alcoolismo"),
            content,  
        ], className='main-section', style={'display': 'flex'}),
        footer,  
    ],
)

# Callbacks
def register_callbacks(app):
    @app.callback(
        Output('dynamic-graphs-subpage3-dashboard3', 'children'),
        [Input('sexo-filter-subpage3-dashboard3', 'value'), Input('raca-filter-subpage3-dashboard3', 'value')]
    )
    def update_graphs(sexo_filter, raca_filter):
        palette_colors = get_current_palette()
        
        filtered_df = df.copy()
        if sexo_filter != 'all':
            filtered_df = filtered_df[filtered_df['sex_feminino'] == sexo_filter]
        
        # Aplicar filtro de raça/cor
        if raca_filter != 'all':
            filtered_df = filtered_df[filtered_df[raca_filter] == 1]

        # Pergunta 24: Distribuição do hábito de fumar por sexo e raça/cor
        smoking_counts = filtered_df['current_smoker'].value_counts()
        smoking_labels = ['Não fumante', 'Fumante atual']
        smoking_values = [smoking_counts.get(False, 0), smoking_counts.get(True, 0)]
        
        graph_1 = create_graph("Distribuição do Hábito de Fumar", {
            'data': [{
                'labels': smoking_labels,
                'values': smoking_values,
                'type': 'pie',
                'marker': {'colors': [palette_colors[1], palette_colors[0]]}
            }],
            'layout': {'title': 'Distribuição do Tabagismo Atual'}
        }, palette_colors)

        # Tabagismo por sexo
        sexo_smoking_data = []
        sexos = ['Masculino', 'Feminino']
        sexo_values = [0, 1]
        
        for i, (sexo, value) in enumerate(zip(sexos, sexo_values)):
            subset = filtered_df[filtered_df['sex_feminino'] == value]
            total = len(subset)
            fumantes = subset['current_smoker'].sum()
            prevalencia = (fumantes / total * 100) if total > 0 else 0
            
            sexo_smoking_data.append({
                'x': [sexo],
                'y': [prevalencia],
                'type': 'bar',
                'name': sexo,
                'marker': {'color': palette_colors[i]},
                'text': [f'{fumantes}/{total}'],
                'textposition': 'auto'
            })
        
        graph_2 = create_graph("Prevalência do Tabagismo por Sexo", {
            'data': sexo_smoking_data,
            'layout': {
                'title': 'Prevalência do Tabagismo por Sexo',
                'xaxis': {'title': 'Sexo'},
                'yaxis': {'title': 'Prevalência (%)', 'tickformat': '.1f', 'ticksuffix': '%'},
                'showlegend': False
            }
        }, palette_colors)

        # Tabagismo por raça/cor
        raca_columns = {
            'ethnicity_race_white': 'Branco',
            'ethnicity_race_pardo': 'Pardo',
            'ethnicity_race_black': 'Preto',
            'ethnicity_race_yellow': 'Amarelo',
            'ethnicity_race_indigenous': 'Indígena'
        }

        raca_smoking_data = []
        for i, (col, label) in enumerate(raca_columns.items()):
            if col in filtered_df.columns:
                subset = filtered_df[filtered_df[col] == 1]
                total = len(subset)
                if total > 0:
                    fumantes = subset['current_smoker'].sum()
                    prevalencia = (fumantes / total * 100)

                    raca_smoking_data.append({
                        'x': [label],
                        'y': [prevalencia],
                        'type': 'bar',
                        'name': label,
                        'marker': {'color': palette_colors[i % len(palette_colors)]},
                        'text': [f'{fumantes}/{total}'],
                        'textposition': 'auto'
                    })

        graph_3 = create_graph("Prevalência do Tabagismo por Raça/Cor", {
            'data': raca_smoking_data,
            'layout': {
                'title': 'Prevalência do Tabagismo por Raça/Cor',
                'xaxis': {'title': 'Raça/Cor'},
                'yaxis': {'title': 'Prevalência (%)', 'tickformat': '.1f', 'ticksuffix': '%'},
                'showlegend': False,
                'annotations': [
                    {
                        'text': 'Sem registros',
                        'x': 'Amarelo',
                        'y': 5,
                        'showarrow': False,
                        'font': {'size': 12, 'color': 'gray'},
                        'xanchor': 'center'
                    }
                ]
            }
        }, palette_colors)

        # Pergunta 27: Distribuição do consumo de álcool
        alcohol_columns = ['alcohol_consumption_none', 'alcohol_consumption_social', 'alcohol_consumption_frequent', 'alcohol_consumption_missing']
        alcohol_labels = ['Não consome', 'Consumo social', 'Consumo frequente', 'Não informado']
        alcohol_values = []

        for col in alcohol_columns:
            if col in filtered_df.columns:
                count = filtered_df[col].sum()
                alcohol_values.append(count)
            else:
                alcohol_values.append(0)

        graph_4 = create_graph("Distribuição do Consumo de Álcool", {
            'data': [{
                'labels': alcohol_labels,
                'values': alcohol_values,
                'type': 'pie',
                'marker': {'colors': palette_colors[:len(alcohol_labels)]}
            }],
            'layout': {'title': 'Padrões de Consumo de Álcool'}
        }, palette_colors)

        # Consumo de álcool por sexo
        sexo_alcohol_data = []
        alcohol_types = {
            'alcohol_consumption_social': 'Consumo Social',
            'alcohol_consumption_frequent': 'Consumo Frequente'
        }

        for i, (col, type_name) in enumerate(alcohol_types.items()):
            if col in filtered_df.columns:
                for j, (sexo, value) in enumerate(zip(sexos, sexo_values)):
                    subset = filtered_df[filtered_df['sex_feminino'] == value]
                    total = len(subset)
                    consumidores = subset[col].sum()
                    prevalencia = (consumidores / total * 100) if total > 0 else 0

                    sexo_alcohol_data.append({
                        'x': [f'{type_name} - {sexo}'],
                        'y': [prevalencia],
                        'type': 'bar',
                        'name': f'{type_name} - {sexo}',
                        'marker': {'color': palette_colors[(i*2 + j) % len(palette_colors)]},
                        'text': [f'{consumidores}/{total}'],
                        'textposition': 'auto'
                    })

        graph_5 = create_graph("Consumo de Álcool por Sexo", {
            'data': sexo_alcohol_data,
            'layout': {
                'title': 'Padrões de Consumo de Álcool por Sexo',
                'xaxis': {'title': 'Tipo de Consumo e Sexo'},
                'yaxis': {'title': 'Prevalência (%)', 'tickformat': '.1f', 'ticksuffix': '%'},
                'showlegend': False,
                'xaxis_tickangle': -45
            }
        }, palette_colors)

        return html.Div([
            html.Div([graph_1, graph_2], className='row'),
            html.Div([graph_3, graph_4], className='row'),
            html.Div([graph_5], className='row')
        ])
