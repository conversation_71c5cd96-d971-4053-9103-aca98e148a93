import pandas as pd
from dash import html, dcc, Input, Output
from components.navbar import create_navbar_for_route
from components.dynamic_sidebar import create_sidebar_for_route
from components.footer import footer
import json
import os
from . import DATA_FILE

def get_current_palette():
    # Paletas de cores definidas
    COLOR_PALETTES = {
        "default": ["#6200EA", "#BB86FC", "#03DAC6", "#018786", "#FFEB3B", "#FFC107", "#FF9800", "#F44336", "#E91E63", "#9C27B0", "#673AB7", "#3F51B5"],
        "colorblind": ["#000000", "#E69F00", "#56B4E9", "#009E73", "#F0E442", "#0072B2", "#D55E00", "#CC79A7", "#999999", "#332288", "#44AA99", "#882255"],
        "autistic": ["#7F95D1", "#7FC9D1", "#7FD195", "#D1C97F", "#D17F95", "#957FD1", "#D1957F", "#95D17F", "#7F95D1", "#7FBCD1", "#D17F95", "#957FD1"],
        "monochrome": ["#0D47A1", "#1565C0", "#1976D2", "#1E88E5", "#2196F3", "#42A5F5", "#64B5F6", "#90CAF9", "#BBDEFB", "#E3F2FD", "#2962FF", "#0D47A1"],
        "pastel": ["#FFB6C1", "#FFD700", "#98FB98", "#ADD8E6", "#FFA07A", "#DDA0DD", "#87CEFA", "#F0E68C", "#E6E6FA", "#FFDAB9", "#B0E0E6", "#FFDEAD"]
    }
    
    config_file = 'config/app_config.json'
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        palette_key = config.get('color_palette', 'default')
    else:
        palette_key = 'default'
    
    return COLOR_PALETTES.get(palette_key, COLOR_PALETTES['default'])

# Carregamento e processamento dos dados
df = pd.read_csv(DATA_FILE)

# Criar idade a partir do ano de nascimento
current_year = 2025
df['idade'] = current_year - df['qual_o_seu_ano_de_nascimento']
df = df[df['idade'].between(0, 110)]

# Processar variáveis de tabagismo
df['fumou_100_cigarros_vida'] = (df['voce_fumou_pelo_menos_100_cigarros_ou_5_macos_em_toda_a_sua_vida'] == 'Sim').astype(int)

# Processar variáveis de alcoolismo
df['bebeu_alcool_alguma_vez_na_vida'] = (df['em_toda_a_sua_vida_voce_ja_bebeu_pelo_menos_1_dose_de_qualquer_tipo_de_bebida_alcoolica_nao_considerar_pequenos_goles'] == 'Sim').astype(int)

# Processar variáveis de hipertensão e diabetes para análises cruzadas
df['tem_hipertensao'] = (df['algum_profissional_de_saude_ja_disse_que_voce_tem_pressao_alta_ou_hipertensao'] == 'Sim').astype(int)
df['diagnostico_diabetes'] = (df['algum_medico_ou_profissional_de_saude_ja_disse_que_voce_tem_diabetes_acucar_no_sangue'] == 'Sim').astype(int)

def create_graph(title, figure, palette_colors=None):
    if palette_colors is None:
        palette_colors = get_current_palette()
    
    if 'data' in figure and figure['data']:
        for i, trace in enumerate(figure['data']):
            if 'marker' in trace:
                if 'color' in trace['marker'] and isinstance(trace['marker']['color'], str):
                    color_index = i % len(palette_colors)
                    trace['marker']['color'] = palette_colors[color_index]
                elif 'colors' in trace['marker'] and isinstance(trace['marker']['colors'], list):
                    num_colors = len(trace['marker']['colors'])
                    trace['marker']['colors'] = palette_colors[:num_colors]
    
    return html.Div([
        html.H3(title, style={'textAlign': 'center'}),
        dcc.Graph(figure=figure)
    ], className='graph-container')

# Layout da subpágina de Tabagismo e Alcoolismo
content = html.Div([
    html.H1("Estatísticas Tabagismo e Alcoolismo - RENOMICA-DAC (GS-004)", style={'textAlign': 'center'}),
    
    # Filtros globais
    html.Div([
        html.Label("Filtrar por Sexo:", style={'marginRight': '10px'}),
        dcc.Dropdown(
            id='sexo-filter-subpage3-dashboard2',
            options=[{'label': 'Todos', 'value': 'all'}] + 
                    [{'label': sexo, 'value': sexo} for sexo in ['Masculino', 'Feminino']],
            value='all',
            clearable=False,
            style={'width': '200px'}
        ),
        html.Label("Filtrar por Raça/Cor:", style={'marginLeft': '20px', 'marginRight': '10px'}),
        dcc.Dropdown(
            id='raca-filter-subpage3-dashboard2',
            options=[{'label': 'Todos', 'value': 'all'}] + 
                    [{'label': raca, 'value': raca} for raca in df['como_voce_se_considera'].dropna().unique()],
            value='all',
            clearable=False,
            style={'width': '200px'}
        )
    ], style={'display': 'flex', 'justifyContent': 'center', 'marginBottom': '20px'}),
    
    # Gráficos base (serão atualizados dinamicamente)
    html.Div(id='dynamic-graphs-subpage3-dashboard2')
], className='content')

# Layout principal com a estrutura comum
layout = html.Div(
    className='page-container',
    children=[
        create_navbar_for_route("/dashboard2/renomica-tabagismo-alcoolismo"),
        html.Div([
            create_sidebar_for_route("/dashboard2/renomica-tabagismo-alcoolismo"),
            content,
        ], className='main-section', style={'display': 'flex'}),
        footer,  
    ],
)

# Callbacks
def register_callbacks(app):
    @app.callback(
        Output('dynamic-graphs-subpage3-dashboard2', 'children'),
        [Input('sexo-filter-subpage3-dashboard2', 'value'), Input('raca-filter-subpage3-dashboard2', 'value')]
    )
    def update_graphs(sexo_filter, raca_filter):
        palette_colors = get_current_palette()
        
        filtered_df = df.copy()
        if sexo_filter != 'all':
            filtered_df = filtered_df[filtered_df['qual_o_seu_sexo_biologico'] == sexo_filter]
        
        if raca_filter != 'all':
            filtered_df = filtered_df[filtered_df['como_voce_se_considera'] == raca_filter]

        # Pergunta 24: Como se distribui o hábito de ter fumado pelo menos 100 cigarros entre os participantes por sexo e raça/cor?
        tabagismo_sexo_data = []
        sexos = ['Masculino', 'Feminino']

        for i, sexo in enumerate(sexos):
            fumantes = len(filtered_df[(filtered_df['qual_o_seu_sexo_biologico'] == sexo) & (filtered_df['fumou_100_cigarros_vida'] == 1)])
            nao_fumantes = len(filtered_df[(filtered_df['qual_o_seu_sexo_biologico'] == sexo) & (filtered_df['fumou_100_cigarros_vida'] == 0)])

            # Cores específicas para cada grupo
            if sexo == 'Masculino':
                cor_fumantes = palette_colors[0]
                cor_nao_fumantes = palette_colors[1]
            else:  # Feminino
                cor_fumantes = palette_colors[2]
                cor_nao_fumantes = palette_colors[3]

            tabagismo_sexo_data.extend([
                {'x': [sexo], 'y': [fumantes], 'name': 'Fumantes', 'type': 'bar', 'marker': {'color': cor_fumantes}, 'legendgroup': 'Fumantes', 'showlegend': i == 0},
                {'x': [sexo], 'y': [nao_fumantes], 'name': 'Não Fumantes', 'type': 'bar', 'marker': {'color': cor_nao_fumantes}, 'legendgroup': 'Não Fumantes', 'showlegend': i == 0}
            ])

        graph_24 = create_graph("Distribuição do Tabagismo por Sexo e Raça/Cor", {
            'data': tabagismo_sexo_data,
            'layout': {
                'barmode': 'group',
                'title': 'Hábito de Fumar por Sexo',
                'xaxis': {'title': 'Sexo'},
                'yaxis': {'title': 'Número de participantes'},
                'showlegend': False,
                'annotations': [
                    {
                        'text': f'<span style="color:{palette_colors[0]}; font-size: 16px; font-weight: bold;">■</span> Fumantes&nbsp;&nbsp;<span style="color:{palette_colors[1]}; font-size: 16px; font-weight: bold;">■</span> Não Fumantes',
                        'x': 0.02,
                        'y': -0.1,
                        'xref': 'paper',
                        'yref': 'paper',
                        'showarrow': False,
                        'align': 'left',
                        'font': {'size': 12, 'color': 'black'},
                        'xanchor': 'left'
                    },
                    {
                        'text': f'<span style="color:{palette_colors[2]}; font-size: 16px; font-weight: bold;">■</span> Fumantes&nbsp;&nbsp;<span style="color:{palette_colors[3]}; font-size: 16px; font-weight: bold;">■</span> Não Fumantes',
                        'x': 0.98,
                        'y': -0.1,
                        'xref': 'paper',
                        'yref': 'paper',
                        'showarrow': False,
                        'align': 'right',
                        'font': {'size': 12, 'color': 'black'},
                        'xanchor': 'right'
                    }
                ]
            }
        }, palette_colors)

        # Pergunta 25: Prevalência de hipertensão entre fumantes vs não fumantes
        hiper_fumantes = len(filtered_df[(filtered_df['fumou_100_cigarros_vida'] == 1) & (filtered_df['tem_hipertensao'] == 1)])
        total_fumantes = len(filtered_df[filtered_df['fumou_100_cigarros_vida'] == 1])
        hiper_nao_fumantes = len(filtered_df[(filtered_df['fumou_100_cigarros_vida'] == 0) & (filtered_df['tem_hipertensao'] == 1)])
        total_nao_fumantes = len(filtered_df[filtered_df['fumou_100_cigarros_vida'] == 0])
        
        prev_hiper_fumantes = (hiper_fumantes / total_fumantes * 100) if total_fumantes > 0 else 0
        prev_hiper_nao_fumantes = (hiper_nao_fumantes / total_nao_fumantes * 100) if total_nao_fumantes > 0 else 0
        
        graph_25 = create_graph("Prevalência de Hipertensão: Fumantes vs Não Fumantes", {
            'data': [{
                'x': ['Fumantes', 'Não Fumantes'],
                'y': [prev_hiper_fumantes, prev_hiper_nao_fumantes],
                'type': 'bar',
                'marker': {'color': [palette_colors[2], palette_colors[3]]}
            }],
            'layout': {'title': 'Prevalência de Hipertensão por Tabagismo', 'xaxis': {'title': 'Grupo'}, 'yaxis': {'title': 'Prevalência (%)'}}
        }, palette_colors)

        # Pergunta 26: Prevalência de diabetes entre fumantes vs não fumantes
        diab_fumantes = len(filtered_df[(filtered_df['fumou_100_cigarros_vida'] == 1) & (filtered_df['diagnostico_diabetes'] == 1)])
        diab_nao_fumantes = len(filtered_df[(filtered_df['fumou_100_cigarros_vida'] == 0) & (filtered_df['diagnostico_diabetes'] == 1)])
        
        prev_diab_fumantes = (diab_fumantes / total_fumantes * 100) if total_fumantes > 0 else 0
        prev_diab_nao_fumantes = (diab_nao_fumantes / total_nao_fumantes * 100) if total_nao_fumantes > 0 else 0
        
        graph_26 = create_graph("Prevalência de Diabetes: Fumantes vs Não Fumantes", {
            'data': [{
                'x': ['Fumantes', 'Não Fumantes'],
                'y': [prev_diab_fumantes, prev_diab_nao_fumantes],
                'type': 'bar',
                'marker': {'color': [palette_colors[4], palette_colors[5]]}
            }],
            'layout': {'title': 'Prevalência de Diabetes por Tabagismo', 'xaxis': {'title': 'Grupo'}, 'yaxis': {'title': 'Prevalência (%)'}}
        }, palette_colors)

        # Pergunta 27: Distribuição do alcoolismo por sexo e raça/cor
        alcoolismo_sexo_data = []
        
        for i, sexo in enumerate(sexos):
            bebedores = len(filtered_df[(filtered_df['qual_o_seu_sexo_biologico'] == sexo) & (filtered_df['bebeu_alcool_alguma_vez_na_vida'] == 1)])
            nao_bebedores = len(filtered_df[(filtered_df['qual_o_seu_sexo_biologico'] == sexo) & (filtered_df['bebeu_alcool_alguma_vez_na_vida'] == 0)])

            # Cores específicas para cada grupo
            if sexo == 'Masculino':
                cor_bebedores = palette_colors[6]
                cor_nao_bebedores = palette_colors[7]
            else:  # Feminino
                cor_bebedores = palette_colors[8]
                cor_nao_bebedores = palette_colors[9]

            alcoolismo_sexo_data.extend([
                {'x': [sexo], 'y': [bebedores], 'name': 'Já beberam', 'type': 'bar', 'marker': {'color': cor_bebedores}, 'legendgroup': 'Já beberam', 'showlegend': i == 0},
                {'x': [sexo], 'y': [nao_bebedores], 'name': 'Nunca beberam', 'type': 'bar', 'marker': {'color': cor_nao_bebedores}, 'legendgroup': 'Nunca beberam', 'showlegend': i == 0}
            ])
        
        graph_27 = html.Div([
            html.H3("Distribuição do Alcoolismo por Sexo e Raça/Cor", style={'textAlign': 'center'}),
            dcc.Graph(figure={
                'data': alcoolismo_sexo_data,
                'layout': {
                    'barmode': 'group',
                    'title': 'Hábito de Beber por Sexo',
                    'xaxis': {'title': 'Sexo'},
                    'yaxis': {'title': 'Número de participantes'},
                    'showlegend': False,
                    'annotations': [
                        {
                            'text': f'<span style="color:{palette_colors[6]}; font-size: 16px; font-weight: bold;">■</span> Já beberam&nbsp;&nbsp;<span style="color:{palette_colors[7]}; font-size: 16px; font-weight: bold;">■</span> Nunca beberam',
                            'x': 0.02,
                            'y': -0.1,
                            'xref': 'paper',
                            'yref': 'paper',
                            'showarrow': False,
                            'align': 'left',
                            'font': {'size': 12, 'color': 'black'},
                            'xanchor': 'left'
                        },
                        {
                            'text': f'<span style="color:{palette_colors[8]}; font-size: 16px; font-weight: bold;">■</span> Já beberam&nbsp;&nbsp;<span style="color:{palette_colors[9]}; font-size: 16px; font-weight: bold;">■</span> Nunca beberam',
                            'x': 0.98,
                            'y': -0.1,
                            'xref': 'paper',
                            'yref': 'paper',
                            'showarrow': False,
                            'align': 'right',
                            'font': {'size': 12, 'color': 'black'},
                            'xanchor': 'right'
                        }
                    ]
                }
            })
        ], className='graph-container')

        # Pergunta 28: Prevalência de hipertensão entre bebedores vs não bebedores
        total_bebedores = len(filtered_df[filtered_df['bebeu_alcool_alguma_vez_na_vida'] == 1])
        total_nao_bebedores = len(filtered_df[filtered_df['bebeu_alcool_alguma_vez_na_vida'] == 0])

        hiper_bebedores = len(filtered_df[(filtered_df['bebeu_alcool_alguma_vez_na_vida'] == 1) & (filtered_df['tem_hipertensao'] == 1)])
        hiper_nao_bebedores = len(filtered_df[(filtered_df['bebeu_alcool_alguma_vez_na_vida'] == 0) & (filtered_df['tem_hipertensao'] == 1)])

        prev_hiper_bebedores = (hiper_bebedores / total_bebedores * 100) if total_bebedores > 0 else 0
        prev_hiper_nao_bebedores = (hiper_nao_bebedores / total_nao_bebedores * 100) if total_nao_bebedores > 0 else 0

        graph_28 = create_graph("Prevalência de Hipertensão: Consomem Bebidas Alcoólicas vs Não Consomem", {
            'data': [{
                'x': ['Consomem álcool', 'Não consomem álcool'],
                'y': [prev_hiper_bebedores, prev_hiper_nao_bebedores],
                'type': 'bar',
                'marker': {'color': [palette_colors[8], palette_colors[9]]}
            }],
            'layout': {'title': 'Prevalência de Hipertensão por Alcoolismo', 'xaxis': {'title': 'Grupo'}, 'yaxis': {'title': 'Prevalência (%)'}}
        }, palette_colors)

        # Pergunta 29: Prevalência de diabetes entre bebedores vs não bebedores
        diab_bebedores = len(filtered_df[(filtered_df['bebeu_alcool_alguma_vez_na_vida'] == 1) & (filtered_df['diagnostico_diabetes'] == 1)])
        diab_nao_bebedores = len(filtered_df[(filtered_df['bebeu_alcool_alguma_vez_na_vida'] == 0) & (filtered_df['diagnostico_diabetes'] == 1)])

        prev_diab_bebedores = (diab_bebedores / total_bebedores * 100) if total_bebedores > 0 else 0
        prev_diab_nao_bebedores = (diab_nao_bebedores / total_nao_bebedores * 100) if total_nao_bebedores > 0 else 0

        graph_29 = create_graph("Prevalência de Diabetes: Consomem Bebidas Alcoólicas vs Não Consomem", {
            'data': [
                {
                    'x': ['Consomem álcool'],
                    'y': [prev_diab_bebedores],
                    'type': 'bar',
                    'name': 'Consomem álcool',
                    'marker': {'color': palette_colors[10]}
                },
                {
                    'x': ['Não consomem álcool'],
                    'y': [prev_diab_nao_bebedores],
                    'type': 'bar',
                    'name': 'Não consomem álcool',
                    'marker': {'color': palette_colors[11]}
                }
            ],
            'layout': {'title': 'Prevalência de Diabetes por Alcoolismo', 'xaxis': {'title': 'Grupo'}, 'yaxis': {'title': 'Prevalência (%)'}}
        }, palette_colors)

        return html.Div([
            html.Div([graph_24, graph_25], className='row'),
            html.Div([graph_26, graph_27], className='row'),
            html.Div([graph_28, graph_29], className='row')
        ])
