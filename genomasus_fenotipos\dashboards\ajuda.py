from dash import html, dcc
from components.navbar import create_navbar_for_route
from components.sidebar import create_sidebar
from components.footer import footer
from components.back_to_home_button import create_back_to_home_button

# Layout da página "Ajuda"
layout = html.Div(
    className='page-container',
    children=[
        # Adicionar o css para o Font Awesome
        html.Link(
            rel='stylesheet',
            href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
        ),

        create_navbar_for_route("/sistema/contato"),
        html.Div(
            className='main-wrapper',
            id='main-wrapper',
            children=[
                create_sidebar("none"),
                html.Div(
                    className='content',
                    style={
                        'padding': '40px 20px',
                        'lineHeight': '1.8',
                        'color': '#333',
                        'backgroundColor': '#f9f9f9',
                        'fontFamily': 'Arial, sans-serif',
                    },
                    children=[
        # Título principal
        html.H1(
            "Suporte ao Projeto GenomaSUS – Fenótipos",
            style={
                'textAlign': 'center',
                'color': '#007BFF',
                'marginBottom': '40px',
                'fontSize': 'clamp(24px, 5vw, 36px)',
                'fontWeight': '600',
            }
        ),

        # Descrição da página
        html.Div(
            style={
                'backgroundColor': '#ffffff',
                'padding': '20px',
                'borderRadius': '10px',
                'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                'marginBottom': '40px',
            },
            children=[
                html.P(
                    "Se precisar de suporte ou tiver dúvidas sobre o projeto GenomaSUS - Fenótipos, entre em contato com nossa equipe pelos e-mails abaixo. Estamos à disposição para ajudar!",
                    style={'color': '#333'}
                ),
            ],
        ),

        # Lista de e-mails
        html.Div(
            style={
                'backgroundColor': '#ffffff',
                'padding': '20px',
                'borderRadius': '10px',
                'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                'marginBottom': '40px',
            },
            children=[
                html.H2("Contatos de Suporte"),
                html.Ul(
                    style={'listStyleType': 'disc', 'paddingLeft': '20px'},
                    children=[
                        html.Li([
                            html.Span(html.Strong("Natale Cavaçana: "), style={'color': '#333'}),
                            html.Span("<EMAIL>", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Nathalia Adriele: "), style={'color': '#333'}),
                            html.Span("<EMAIL>", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Giovanna Giudicelli: "), style={'color': '#333'}),
                            html.Span("<EMAIL>", style={'color': '#333'})
                        ]),
                        html.Li([
                            html.Span(html.Strong("Eduardo Gade: "), style={'color': '#333'}),
                            html.Span("<EMAIL>", style={'color': '#333'})
                        ]),
                    ]
                ),
            ],
        ),

        # Botão padronizado "Voltar à Página Inicial"
        create_back_to_home_button(),
                    ]
                ),
            ],
        ),
        footer,
    ],
)